import 'dart:async';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../app/app.locator.dart';
import 'logging_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  String? _accessToken;
  String? _refreshToken;
  String? _verificationId;

  // Get logging service - will be available after locator setup
  LoggingService? get _logger {
    try {
      return locator<LoggingService>();
    } catch (e) {
      return null; // Fallback if locator not ready
    }
  }

  // Replace with your backend URL
  final String baseUrl = 'https://your-backend-url.com';

  // Getters
  String? get accessToken => _accessToken;
  String? get refreshToken => _refreshToken;
  User? get currentUser => _firebaseAuth.currentUser;
  bool get isAuthenticated => _accessToken != null;

  // Initialize the service
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString('access_token');
    _refreshToken = prefs.getString('refresh_token');
  }

  // Save tokens to local storage
  Future<void> _saveTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', accessToken);
    await prefs.setString('refresh_token', refreshToken);
    _accessToken = accessToken;
    _refreshToken = refreshToken;
  }

  // Clear tokens from local storage
  Future<void> _clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    await prefs.remove('refresh_token');
    _accessToken = null;
    _refreshToken = null;
  }

  // Start phone authentication
  Future<void> authenticateWithPhone(String phoneNumber) async {
    try {
      final completer = Completer<void>();

      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            final userCredential = await _firebaseAuth.signInWithCredential(credential);
            final idToken = await userCredential.user?.getIdToken();
            if (idToken != null) {
              await verifyIdToken(idToken);
            }
            completer.complete();
          } catch (e) {
            completer.completeError(e);
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          completer.completeError(e);
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          completer.complete();
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
      );

      await completer.future;
    } catch (e) {
      throw Exception('Phone authentication failed: $e');
    }
  }

  // Verify OTP code
  Future<Map<String, dynamic>> verifyOTP(String otpCode) async {
    try {
      if (_verificationId == null) {
        throw Exception('No verification ID found. Please restart the authentication process.');
      }

      final credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: otpCode,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      final idToken = await userCredential.user?.getIdToken();

      if (idToken != null) {
        return await verifyIdToken(idToken);
      } else {
        throw Exception('Failed to get ID token');
      }
    } catch (e) {
      throw Exception('OTP verification failed: $e');
    }
  }

  // Verify ID token with backend
  Future<Map<String, dynamic>> verifyIdToken(String idToken) async {
    try {
      // Check if backend URL is still placeholder
      if (baseUrl.contains('your-backend-url.com')) {
        _logger?.warning('Backend URL not configured. Skipping backend verification for testing.');
        _logger?.logAuth('Firebase authentication successful!');

        // Create mock tokens for testing
        await _saveTokens(
          'mock_access_token_${DateTime.now().millisecondsSinceEpoch}',
          'mock_refresh_token_${DateTime.now().millisecondsSinceEpoch}',
        );

        return {
          'success': true,
          'message': 'Authentication successful (Firebase only)',
          'user': {
            'id': _firebaseAuth.currentUser?.uid,
            'phoneNumber': _firebaseAuth.currentUser?.phoneNumber,
            'createdAt': DateTime.now().toIso8601String(),
          }
        };
      }

      _logger?.logApiRequest('POST', '$baseUrl/auth/verify-token', {'idToken': '${idToken.substring(0, 50)}...'});

      final response = await http.post(
        Uri.parse('$baseUrl/auth/verify-token'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'idToken': idToken}),
      );

      _logger?.logApiResponse(response.statusCode, response.body);

      if (response.body.isEmpty) {
        throw Exception('Backend returned empty response. Check if backend is running at $baseUrl');
      }

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success']) {
        await _saveTokens(
          data['tokens']['accessToken'],
          data['tokens']['refreshToken'],
        );
        return data;
      } else {
        throw Exception(data['message'] ?? 'Authentication failed');
      }
    } catch (e) {
      if (e is FormatException) {
        throw Exception('Backend returned invalid JSON. Response might be HTML error page. Check backend URL: $baseUrl');
      }
      throw Exception('Token verification failed: $e');
    }
  }

  // Refresh access token
  Future<Map<String, dynamic>> refreshAccessToken() async {
    if (_refreshToken == null) {
      throw Exception('No refresh token available');
    }

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/refresh-token'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'refreshToken': _refreshToken}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success']) {
        await _saveTokens(
          data['tokens']['accessToken'],
          data['tokens']['refreshToken'],
        );
        return data;
      } else {
        throw Exception(data['message'] ?? 'Token refresh failed');
      }
    } catch (e) {
      throw Exception('Token refresh failed: $e');
    }
  }

  // Get user profile
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      // Check if backend URL is still placeholder
      if (baseUrl.contains('your-backend-url.com')) {
        _logger?.warning('Backend URL not configured. Returning Firebase user data.');

        return {
          'success': true,
          'user': {
            'id': _firebaseAuth.currentUser?.uid ?? 'unknown',
            'phoneNumber': _firebaseAuth.currentUser?.phoneNumber ?? 'N/A',
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
          }
        };
      }

      final response = await http.get(
        Uri.parse('$baseUrl/auth/me'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_accessToken',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success']) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get profile');
      }
    } catch (e) {
      throw Exception('Failed to get profile: $e');
    }
  }

  // Update user profile
  Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/auth/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_accessToken',
        },
        body: jsonEncode(profileData),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success']) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Profile update failed');
      }
    } catch (e) {
      throw Exception('Profile update failed: $e');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _clearTokens();
    } catch (e) {
      throw Exception('Sign out failed: $e');
    }
  }

  // Resend OTP
  Future<void> resendOTP(String phoneNumber) async {
    await authenticateWithPhone(phoneNumber);
  }
}
