import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();

  late final Logger _logger;

  void initialize() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 5,
        lineLength: 50,
        colors: true,
        printEmojis: true,
        printTime: false,
      ),
      level: kDebugMode ? Level.debug : Level.info,
    );
  }

  void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      _logger.d(message, error: error, stackTrace: stackTrace);
    }
  }

  void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  void verbose(String message, [dynamic error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      _logger.t(message, error: error, stackTrace: stackTrace);
    }
  }

  // Convenience methods for common logging patterns
  void logApiRequest(String method, String url, [Map<String, dynamic>? body]) {
    if (kDebugMode) {
      debug('🔗 API $method: $url');
      if (body != null) {
        debug('📤 Request body: ${body.toString().substring(0, body.toString().length > 100 ? 100 : body.toString().length)}...');
      }
    }
  }

  void logApiResponse(int statusCode, String body) {
    if (kDebugMode) {
      debug('📥 Response status: $statusCode');
      debug('📥 Response body: ${body.length > 200 ? body.substring(0, 200) + '...' : body}');
    }
  }

  void logNavigation(String from, String to) {
    if (kDebugMode) {
      debug('🧭 Navigation: $from → $to');
    }
  }

  void logAuth(String action, [String? details]) {
    if (kDebugMode) {
      debug('🔐 Auth: $action${details != null ? ' - $details' : ''}');
    }
  }

  void logFirebase(String action, [String? details]) {
    if (kDebugMode) {
      debug('🔥 Firebase: $action${details != null ? ' - $details' : ''}');
    }
  }

  void logApp(String action, [String? details]) {
    if (kDebugMode) {
      debug('🚀 App: $action${details != null ? ' - $details' : ''}');
    }
  }
}
