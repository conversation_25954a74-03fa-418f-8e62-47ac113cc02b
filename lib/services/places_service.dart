import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class PlacesService {
  // Google Maps API key
  static const String _googleMapsApiKey = 'AIzaSyDj7ujM3r78qkQ6qTHETQwgdvMvXNSHm_w';
  
  // Base URLs for Google Places API
  static const String _placesBaseUrl = 'https://maps.googleapis.com/maps/api/place';
  static const String _geocodingBaseUrl = 'https://maps.googleapis.com/maps/api/geocode/json';

  /// Search for places using autocomplete
  Future<List<PlacePrediction>> searchPlaces(String query) async {
    if (query.isEmpty) return [];

    try {
      final url = Uri.parse('$_placesBaseUrl/autocomplete/json?'
          'input=${Uri.encodeComponent(query)}'
          '&key=$_googleMapsApiKey'
          '&components=country:in'); // Restrict to India

      debugPrint('Places Autocomplete URL: $url');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parsePlacePredictions(data);
      } else {
        throw Exception('Failed to search places: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error searching places: $e');
      return [];
    }
  }

  /// Get place details including coordinates
  Future<PlaceDetails?> getPlaceDetails(String placeId) async {
    try {
      final url = Uri.parse('$_placesBaseUrl/details/json?'
          'place_id=$placeId'
          '&fields=name,formatted_address,geometry,address_components'
          '&key=$_googleMapsApiKey');

      debugPrint('Place Details URL: $url');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parsePlaceDetails(data);
      } else {
        throw Exception('Failed to get place details: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting place details: $e');
      return null;
    }
  }

  /// Reverse geocode coordinates to get address
  Future<String> reverseGeocode(double latitude, double longitude) async {
    try {
      final url = Uri.parse('$_geocodingBaseUrl?'
          'latlng=$latitude,$longitude'
          '&key=$_googleMapsApiKey');

      debugPrint('Reverse Geocoding URL: $url');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseReverseGeocodeResponse(data);
      } else {
        throw Exception('Failed to reverse geocode: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error reverse geocoding: $e');
      return 'Unknown location';
    }
  }

  // Helper methods
  List<PlacePrediction> _parsePlacePredictions(Map<String, dynamic> data) {
    final status = data['status'];
    if (status != 'OK') {
      debugPrint('Places API error: $status');
      return [];
    }

    final predictions = data['predictions'] as List<dynamic>;
    return predictions.map((prediction) => PlacePrediction(
      placeId: prediction['place_id'],
      description: prediction['description'],
      mainText: prediction['structured_formatting']['main_text'],
      secondaryText: prediction['structured_formatting']['secondary_text'] ?? '',
    )).toList();
  }

  PlaceDetails? _parsePlaceDetails(Map<String, dynamic> data) {
    final status = data['status'];
    if (status != 'OK') {
      debugPrint('Place Details API error: $status');
      return null;
    }

    final result = data['result'];
    final geometry = result['geometry'];
    final location = geometry['location'];

    return PlaceDetails(
      placeId: result['place_id'] ?? '',
      name: result['name'] ?? '',
      formattedAddress: result['formatted_address'] ?? '',
      latitude: location['lat']?.toDouble() ?? 0.0,
      longitude: location['lng']?.toDouble() ?? 0.0,
    );
  }

  String _parseReverseGeocodeResponse(Map<String, dynamic> data) {
    final status = data['status'];
    if (status != 'OK') {
      debugPrint('Reverse Geocoding API error: $status');
      return 'Unknown location';
    }

    final results = data['results'] as List<dynamic>;
    if (results.isNotEmpty) {
      return results[0]['formatted_address'] ?? 'Unknown location';
    }

    return 'Unknown location';
  }
}

/// Place prediction from autocomplete
class PlacePrediction {
  final String placeId;
  final String description;
  final String mainText;
  final String secondaryText;

  PlacePrediction({
    required this.placeId,
    required this.description,
    required this.mainText,
    required this.secondaryText,
  });

  @override
  String toString() {
    return 'PlacePrediction(placeId: $placeId, description: $description)';
  }
}

/// Detailed place information
class PlaceDetails {
  final String placeId;
  final String name;
  final String formattedAddress;
  final double latitude;
  final double longitude;

  PlaceDetails({
    required this.placeId,
    required this.name,
    required this.formattedAddress,
    required this.latitude,
    required this.longitude,
  });

  @override
  String toString() {
    return 'PlaceDetails(name: $name, address: $formattedAddress, lat: $latitude, lng: $longitude)';
  }
}
