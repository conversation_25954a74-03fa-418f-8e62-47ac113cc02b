import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/address_model.dart';

class AddressService {
  static const String _addressesKey = 'saved_addresses';
  static const String _defaultAddressKey = 'default_address_id';

  // Get all saved addresses
  Future<List<AddressModel>> getSavedAddresses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final addressesJson = prefs.getString(_addressesKey);
      
      if (addressesJson == null || addressesJson.isEmpty) {
        return [];
      }

      final List<dynamic> addressesList = jsonDecode(addressesJson);
      return addressesList
          .map((json) => AddressModel.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Error getting saved addresses: $e');
      return [];
    }
  }

  // Save a new address
  Future<bool> saveAddress(AddressModel address) async {
    try {
      final addresses = await getSavedAddresses();
      
      // Generate ID if not provided
      final newAddress = address.id == null 
          ? address.copyWith(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              updatedAt: DateTime.now(),
            )
          : address.copyWith(updatedAt: DateTime.now());

      // Check if address already exists (by coordinates)
      final existingIndex = addresses.indexWhere((addr) => 
          addr.latitude == newAddress.latitude && 
          addr.longitude == newAddress.longitude);

      if (existingIndex != -1) {
        // Update existing address
        addresses[existingIndex] = newAddress;
      } else {
        // Add new address
        addresses.add(newAddress);
      }

      // If this is the first address or marked as default, set as default
      if (addresses.length == 1 || newAddress.isDefault) {
        await _setDefaultAddress(newAddress.id!);
        
        // Update all other addresses to not be default
        for (int i = 0; i < addresses.length; i++) {
          if (addresses[i].id != newAddress.id) {
            addresses[i] = addresses[i].copyWith(isDefault: false);
          }
        }
      }

      return await _saveAddressesList(addresses);
    } catch (e) {
      debugPrint('Error saving address: $e');
      return false;
    }
  }

  // Update an existing address
  Future<bool> updateAddress(AddressModel address) async {
    try {
      final addresses = await getSavedAddresses();
      final index = addresses.indexWhere((addr) => addr.id == address.id);
      
      if (index == -1) {
        debugPrint('Address not found for update');
        return false;
      }

      addresses[index] = address.copyWith(updatedAt: DateTime.now());

      // If marked as default, update other addresses
      if (address.isDefault) {
        await _setDefaultAddress(address.id!);
        for (int i = 0; i < addresses.length; i++) {
          if (addresses[i].id != address.id) {
            addresses[i] = addresses[i].copyWith(isDefault: false);
          }
        }
      }

      return await _saveAddressesList(addresses);
    } catch (e) {
      debugPrint('Error updating address: $e');
      return false;
    }
  }

  // Delete an address
  Future<bool> deleteAddress(String addressId) async {
    try {
      final addresses = await getSavedAddresses();
      final addressToDelete = addresses.firstWhere(
        (addr) => addr.id == addressId,
        orElse: () => throw Exception('Address not found'),
      );

      addresses.removeWhere((addr) => addr.id == addressId);

      // If deleted address was default, set first remaining as default
      if (addressToDelete.isDefault && addresses.isNotEmpty) {
        addresses[0] = addresses[0].copyWith(isDefault: true);
        await _setDefaultAddress(addresses[0].id!);
      } else if (addressToDelete.isDefault) {
        await _clearDefaultAddress();
      }

      return await _saveAddressesList(addresses);
    } catch (e) {
      debugPrint('Error deleting address: $e');
      return false;
    }
  }

  // Get default address
  Future<AddressModel?> getDefaultAddress() async {
    try {
      final addresses = await getSavedAddresses();
      return addresses.firstWhere(
        (addr) => addr.isDefault,
        orElse: () => addresses.isNotEmpty ? addresses.first : throw Exception('No addresses found'),
      );
    } catch (e) {
      debugPrint('Error getting default address: $e');
      return null;
    }
  }

  // Set default address
  Future<bool> setDefaultAddress(String addressId) async {
    try {
      final addresses = await getSavedAddresses();
      bool found = false;

      for (int i = 0; i < addresses.length; i++) {
        if (addresses[i].id == addressId) {
          addresses[i] = addresses[i].copyWith(isDefault: true);
          found = true;
        } else {
          addresses[i] = addresses[i].copyWith(isDefault: false);
        }
      }

      if (!found) {
        debugPrint('Address not found for setting as default');
        return false;
      }

      await _setDefaultAddress(addressId);
      return await _saveAddressesList(addresses);
    } catch (e) {
      debugPrint('Error setting default address: $e');
      return false;
    }
  }

  // Clear all addresses
  Future<bool> clearAllAddresses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_addressesKey);
      await prefs.remove(_defaultAddressKey);
      return true;
    } catch (e) {
      debugPrint('Error clearing addresses: $e');
      return false;
    }
  }

  // Private helper methods
  Future<bool> _saveAddressesList(List<AddressModel> addresses) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final addressesJson = jsonEncode(addresses.map((addr) => addr.toJson()).toList());
      return await prefs.setString(_addressesKey, addressesJson);
    } catch (e) {
      debugPrint('Error saving addresses list: $e');
      return false;
    }
  }

  Future<void> _setDefaultAddress(String addressId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_defaultAddressKey, addressId);
  }

  Future<void> _clearDefaultAddress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_defaultAddressKey);
  }

  // Search addresses by query
  Future<List<AddressModel>> searchAddresses(String query) async {
    try {
      final addresses = await getSavedAddresses();
      if (query.isEmpty) return addresses;

      return addresses.where((address) {
        final searchQuery = query.toLowerCase();
        return address.title.toLowerCase().contains(searchQuery) ||
               address.fullAddress.toLowerCase().contains(searchQuery) ||
               (address.landmark?.toLowerCase().contains(searchQuery) ?? false);
      }).toList();
    } catch (e) {
      debugPrint('Error searching addresses: $e');
      return [];
    }
  }
}
