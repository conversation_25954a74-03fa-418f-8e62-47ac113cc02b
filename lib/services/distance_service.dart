import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:latlong2/latlong.dart';

class DistanceService {
  // Google Maps API key
  static const String _googleMapsApiKey = 'AIzaSyDj7ujM3r78qkQ6qTHETQwgdvMvXNSHm_w';

  // Base URLs for Google APIs
  static const String _distanceMatrixBaseUrl = 'https://maps.googleapis.com/maps/api/distancematrix/json';
  static const String _directionsBaseUrl = 'https://maps.googleapis.com/maps/api/directions/json';

  /// Calculate straight-line distance between two points using Haversine formula
  /// Returns distance in kilometers
  static double calculateStraightLineDistance({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
  }) {
    const Distance distance = Distance();
    final LatLng point1 = LatLng(lat1, lon1);
    final LatLng point2 = LatLng(lat2, lon2);

    // Returns distance in meters, convert to kilometers
    return distance.as(LengthUnit.Kilometer, point1, point2);
  }

  /// Calculate road distance using Google Distance Matrix API
  /// Returns DistanceResult with distance and route info
  Future<DistanceResult> calculateRoadDistance({
    required double originLat,
    required double originLng,
    required double destinationLat,
    required double destinationLng,
    String travelMode = 'driving',
    bool avoidTolls = false,
    bool avoidHighways = false,
  }) async {
    try {
      final origin = '$originLat,$originLng';
      final destination = '$destinationLat,$destinationLng';

      final url = Uri.parse('$_distanceMatrixBaseUrl?'
          'origins=$origin'
          '&destinations=$destination'
          '&mode=$travelMode'
          '&units=metric'
          '&avoid=${_buildAvoidString(avoidTolls, avoidHighways)}'
          '&key=$_googleMapsApiKey');

      debugPrint('Distance Matrix API URL: $url');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseDistanceMatrixResponse(data);
      } else {
        throw Exception('Failed to get distance data: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error calculating road distance: $e');
      // Fallback to straight-line distance
      final straightDistance = calculateStraightLineDistance(
        lat1: originLat,
        lon1: originLng,
        lat2: destinationLat,
        lon2: destinationLng,
      );

      return DistanceResult(
        distance: straightDistance,
        duration: _estimateDuration(straightDistance, travelMode),
        distanceText: '${straightDistance.toStringAsFixed(1)} km (approx)',
        durationText: '${_estimateDuration(straightDistance, travelMode)} min (approx)',
        status: 'FALLBACK',
        isApproximate: true,
      );
    }
  }

  /// Get detailed route information with turn-by-turn directions
  Future<RouteResult> getDetailedRoute({
    required double originLat,
    required double originLng,
    required double destinationLat,
    required double destinationLng,
    String travelMode = 'driving',
    bool avoidTolls = false,
    bool avoidHighways = false,
  }) async {
    try {
      final origin = '$originLat,$originLng';
      final destination = '$destinationLat,$destinationLng';

      final url = Uri.parse('$_directionsBaseUrl?'
          'origin=$origin'
          '&destination=$destination'
          '&mode=$travelMode'
          '&avoid=${_buildAvoidString(avoidTolls, avoidHighways)}'
          '&key=$_googleMapsApiKey');

      debugPrint('Directions API URL: $url');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseDirectionsResponse(data);
      } else {
        throw Exception('Failed to get route data: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting detailed route: $e');
      rethrow;
    }
  }



  // Helper methods
  String _buildAvoidString(bool avoidTolls, bool avoidHighways) {
    final avoid = <String>[];
    if (avoidTolls) avoid.add('tolls');
    if (avoidHighways) avoid.add('highways');
    return avoid.join('|');
  }

  DistanceResult _parseDistanceMatrixResponse(Map<String, dynamic> data) {
    final status = data['status'];
    if (status != 'OK') {
      throw Exception('Distance Matrix API error: $status');
    }

    final elements = data['rows'][0]['elements'][0];
    final elementStatus = elements['status'];

    if (elementStatus != 'OK') {
      throw Exception('No route found: $elementStatus');
    }

    final distance = elements['distance'];
    final duration = elements['duration'];

    return DistanceResult(
      distance: distance['value'] / 1000.0, // Convert meters to kilometers
      duration: duration['value'] / 60.0, // Convert seconds to minutes
      distanceText: distance['text'],
      durationText: duration['text'],
      status: elementStatus,
      isApproximate: false,
    );
  }

  RouteResult _parseDirectionsResponse(Map<String, dynamic> data) {
    final status = data['status'];
    if (status != 'OK') {
      throw Exception('Directions API error: $status');
    }

    final route = data['routes'][0];
    final leg = route['legs'][0];

    return RouteResult(
      distance: leg['distance']['value'] / 1000.0,
      duration: leg['duration']['value'] / 60.0,
      distanceText: leg['distance']['text'],
      durationText: leg['duration']['text'],
      startAddress: leg['start_address'],
      endAddress: leg['end_address'],
      polylinePoints: route['overview_polyline']['points'],
      steps: _parseSteps(leg['steps']),
    );
  }

  List<RouteStep> _parseSteps(List<dynamic> steps) {
    return steps.map((step) => RouteStep(
      distance: step['distance']['text'],
      duration: step['duration']['text'],
      instruction: step['html_instructions'],
      startLat: step['start_location']['lat'],
      startLng: step['start_location']['lng'],
      endLat: step['end_location']['lat'],
      endLng: step['end_location']['lng'],
    )).toList();
  }

  double _estimateDuration(double distanceKm, String travelMode) {
    // Assume 30 km/h average speed for driving in city
    return distanceKm * 2;
  }
}

/// Result class for distance calculations
class DistanceResult {
  final double distance; // in kilometers
  final double duration; // in minutes
  final String distanceText;
  final String durationText;
  final String status;
  final bool isApproximate;

  DistanceResult({
    required this.distance,
    required this.duration,
    required this.distanceText,
    required this.durationText,
    required this.status,
    required this.isApproximate,
  });

  @override
  String toString() {
    return 'DistanceResult(distance: ${distance.toStringAsFixed(1)}km, '
           'duration: ${duration.toStringAsFixed(0)}min, '
           'approximate: $isApproximate)';
  }
}

/// Result class for detailed route information
class RouteResult {
  final double distance;
  final double duration;
  final String distanceText;
  final String durationText;
  final String startAddress;
  final String endAddress;
  final String polylinePoints;
  final List<RouteStep> steps;

  RouteResult({
    required this.distance,
    required this.duration,
    required this.distanceText,
    required this.durationText,
    required this.startAddress,
    required this.endAddress,
    required this.polylinePoints,
    required this.steps,
  });
}

/// Individual step in a route
class RouteStep {
  final String distance;
  final String duration;
  final String instruction;
  final double startLat;
  final double startLng;
  final double endLat;
  final double endLng;

  RouteStep({
    required this.distance,
    required this.duration,
    required this.instruction,
    required this.startLat,
    required this.startLng,
    required this.endLat,
    required this.endLng,
  });
}
