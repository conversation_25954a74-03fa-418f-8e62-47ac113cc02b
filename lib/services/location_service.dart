import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:http/http.dart' as http;

class LocationService {
  static const String _googleMapsApiKey = 'YOUR_GOOGLE_MAPS_API_KEY';
  static const String _geocodingBaseUrl = 'https://maps.googleapis.com/maps/api/geocode/json';
  static const String _placesBaseUrl = 'https://maps.googleapis.com/maps/api/place';

  /// Check if location services are enabled and permissions are granted
  Future<LocationPermissionStatus> checkLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationPermissionStatus.serviceDisabled;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      
      switch (permission) {
        case LocationPermission.denied:
          return LocationPermissionStatus.denied;
        case LocationPermission.deniedForever:
          return LocationPermissionStatus.deniedForever;
        case LocationPermission.whileInUse:
        case LocationPermission.always:
          return LocationPermissionStatus.granted;
        default:
          return LocationPermissionStatus.denied;
      }
    } catch (e) {
      debugPrint('Error checking location permission: $e');
      return LocationPermissionStatus.error;
    }
  }

  /// Request location permission
  Future<LocationPermissionStatus> requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.requestPermission();
      
      switch (permission) {
        case LocationPermission.denied:
          return LocationPermissionStatus.denied;
        case LocationPermission.deniedForever:
          return LocationPermissionStatus.deniedForever;
        case LocationPermission.whileInUse:
        case LocationPermission.always:
          return LocationPermissionStatus.granted;
        default:
          return LocationPermissionStatus.denied;
      }
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      return LocationPermissionStatus.error;
    }
  }

  /// Get current location
  Future<LocationResult> getCurrentLocation() async {
    try {
      final permissionStatus = await checkLocationPermission();
      if (permissionStatus != LocationPermissionStatus.granted) {
        final requestResult = await requestLocationPermission();
        if (requestResult != LocationPermissionStatus.granted) {
          throw LocationException('Location permission not granted');
        }
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return LocationResult(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: position.timestamp ?? DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error getting current location: $e');
      throw LocationException('Failed to get current location: $e');
    }
  }

  /// Convert coordinates to address using reverse geocoding
  Future<AddressResult> getAddressFromCoordinates({
    required double latitude,
    required double longitude,
    bool useGoogleApi = true,
  }) async {
    try {
      if (useGoogleApi && _googleMapsApiKey != 'YOUR_GOOGLE_MAPS_API_KEY') {
        return await _getAddressFromGoogleApi(latitude, longitude);
      } else {
        return await _getAddressFromGeocodingPackage(latitude, longitude);
      }
    } catch (e) {
      debugPrint('Error getting address from coordinates: $e');
      throw LocationException('Failed to get address: $e');
    }
  }

  /// Get coordinates from address using forward geocoding
  Future<LocationResult> getCoordinatesFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        final location = locations.first;
        return LocationResult(
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: 0.0,
          timestamp: DateTime.now(),
        );
      } else {
        throw LocationException('No coordinates found for address');
      }
    } catch (e) {
      debugPrint('Error getting coordinates from address: $e');
      throw LocationException('Failed to get coordinates: $e');
    }
  }

  /// Search for places using Google Places API
  Future<List<PlaceResult>> searchPlaces({
    required String query,
    double? latitude,
    double? longitude,
    int radius = 50000, // 50km radius
  }) async {
    try {
      if (_googleMapsApiKey == 'YOUR_GOOGLE_MAPS_API_KEY') {
        throw LocationException('Google Maps API key not configured');
      }

      String url = '$_placesBaseUrl/textsearch/json?'
          'query=${Uri.encodeComponent(query)}'
          '&key=$_googleMapsApiKey';

      if (latitude != null && longitude != null) {
        url += '&location=$latitude,$longitude&radius=$radius';
      }

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parsePlacesResponse(data);
      } else {
        throw LocationException('Places API error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error searching places: $e');
      throw LocationException('Failed to search places: $e');
    }
  }

  /// Get place details by place ID
  Future<PlaceDetails> getPlaceDetails(String placeId) async {
    try {
      if (_googleMapsApiKey == 'YOUR_GOOGLE_MAPS_API_KEY') {
        throw LocationException('Google Maps API key not configured');
      }

      final url = '$_placesBaseUrl/details/json?'
          'place_id=$placeId'
          '&fields=name,formatted_address,geometry,formatted_phone_number,rating'
          '&key=$_googleMapsApiKey';

      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parsePlaceDetailsResponse(data);
      } else {
        throw LocationException('Place Details API error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting place details: $e');
      throw LocationException('Failed to get place details: $e');
    }
  }

  // Private helper methods
  Future<AddressResult> _getAddressFromGoogleApi(double latitude, double longitude) async {
    final url = '$_geocodingBaseUrl?'
        'latlng=$latitude,$longitude'
        '&key=$_googleMapsApiKey';

    final response = await http.get(Uri.parse(url));
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['status'] == 'OK' && data['results'].isNotEmpty) {
        final result = data['results'][0];
        return AddressResult(
          formattedAddress: result['formatted_address'],
          street: _extractAddressComponent(result, 'route'),
          city: _extractAddressComponent(result, 'locality'),
          state: _extractAddressComponent(result, 'administrative_area_level_1'),
          country: _extractAddressComponent(result, 'country'),
          postalCode: _extractAddressComponent(result, 'postal_code'),
        );
      }
    }
    throw LocationException('Failed to get address from Google API');
  }

  Future<AddressResult> _getAddressFromGeocodingPackage(double latitude, double longitude) async {
    final placemarks = await placemarkFromCoordinates(latitude, longitude);
    if (placemarks.isNotEmpty) {
      final placemark = placemarks.first;
      return AddressResult(
        formattedAddress: '${placemark.street}, ${placemark.locality}, ${placemark.administrativeArea}, ${placemark.country}',
        street: placemark.street ?? '',
        city: placemark.locality ?? '',
        state: placemark.administrativeArea ?? '',
        country: placemark.country ?? '',
        postalCode: placemark.postalCode ?? '',
      );
    }
    throw LocationException('No address found');
  }

  String _extractAddressComponent(Map<String, dynamic> result, String type) {
    final components = result['address_components'] as List;
    for (final component in components) {
      final types = component['types'] as List;
      if (types.contains(type)) {
        return component['long_name'];
      }
    }
    return '';
  }

  List<PlaceResult> _parsePlacesResponse(Map<String, dynamic> data) {
    final results = data['results'] as List;
    return results.map((result) => PlaceResult(
      placeId: result['place_id'],
      name: result['name'],
      formattedAddress: result['formatted_address'],
      latitude: result['geometry']['location']['lat'],
      longitude: result['geometry']['location']['lng'],
      rating: result['rating']?.toDouble(),
    )).toList();
  }

  PlaceDetails _parsePlaceDetailsResponse(Map<String, dynamic> data) {
    final result = data['result'];
    return PlaceDetails(
      placeId: result['place_id'],
      name: result['name'],
      formattedAddress: result['formatted_address'],
      latitude: result['geometry']['location']['lat'],
      longitude: result['geometry']['location']['lng'],
      phoneNumber: result['formatted_phone_number'],
      rating: result['rating']?.toDouble(),
    );
  }
}

// Enums and Data Classes
enum LocationPermissionStatus {
  granted,
  denied,
  deniedForever,
  serviceDisabled,
  error,
}

class LocationResult {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;

  LocationResult({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'LocationResult(lat: $latitude, lng: $longitude, accuracy: $accuracy)';
  }
}

class AddressResult {
  final String formattedAddress;
  final String street;
  final String city;
  final String state;
  final String country;
  final String postalCode;

  AddressResult({
    required this.formattedAddress,
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
  });

  @override
  String toString() {
    return 'AddressResult(address: $formattedAddress)';
  }
}

class PlaceResult {
  final String placeId;
  final String name;
  final String formattedAddress;
  final double latitude;
  final double longitude;
  final double? rating;

  PlaceResult({
    required this.placeId,
    required this.name,
    required this.formattedAddress,
    required this.latitude,
    required this.longitude,
    this.rating,
  });
}

class PlaceDetails {
  final String placeId;
  final String name;
  final String formattedAddress;
  final double latitude;
  final double longitude;
  final String? phoneNumber;
  final double? rating;

  PlaceDetails({
    required this.placeId,
    required this.name,
    required this.formattedAddress,
    required this.latitude,
    required this.longitude,
    this.phoneNumber,
    this.rating,
  });
}

class LocationException implements Exception {
  final String message;
  LocationException(this.message);

  @override
  String toString() => 'LocationException: $message';
}
