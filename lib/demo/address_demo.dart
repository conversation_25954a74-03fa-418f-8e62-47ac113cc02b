import 'package:flutter/material.dart';
import '../models/address_model.dart';
import '../services/address_service.dart';

/// Demo class to show how the address functionality works
class AddressDemo {
  static void demonstrateAddressModel() {
    debugPrint('=== Address Model Demo ===');
    
    // Create a new address
    final homeAddress = AddressModel(
      title: 'Home',
      fullAddress: '123 Main Street, New Delhi, India',
      landmark: 'Near Metro Station',
      latitude: 28.6139,
      longitude: 77.2090,
      addressType: 'home',
      contactName: '<PERSON>',
      contactPhone: '+91 9876543210',
      isDefault: true,
    );

    debugPrint('Created address: ${homeAddress.title}');
    debugPrint('Full address: ${homeAddress.fullAddress}');
    debugPrint('Display address: ${homeAddress.displayAddress}');
    debugPrint('Type icon: ${homeAddress.typeIcon}');
    debugPrint('Is default: ${homeAddress.isDefault}');

    // Convert to JSON and back
    final json = homeAddress.toJson();
    debugPrint('JSON: $json');
    
    final recreatedAddress = AddressModel.fromJson(json);
    debugPrint('Recreated address title: ${recreatedAddress.title}');

    // Test copyWith
    final updatedAddress = homeAddress.copyWith(
      title: 'Updated Home',
      isDefault: false,
    );
    debugPrint('Updated address title: ${updatedAddress.title}');
    debugPrint('Updated address is default: ${updatedAddress.isDefault}');
  }

  static Future<void> demonstrateAddressService() async {
    debugPrint('=== Address Service Demo ===');
    
    final addressService = AddressService();

    // Create sample addresses
    final homeAddress = AddressModel(
      title: 'Home',
      fullAddress: '123 Home Street, Delhi',
      latitude: 28.6139,
      longitude: 77.2090,
      addressType: 'home',
      isDefault: true,
    );

    final workAddress = AddressModel(
      title: 'Office',
      fullAddress: '456 Business Avenue, Gurgaon',
      latitude: 28.4595,
      longitude: 77.0266,
      addressType: 'work',
      isDefault: false,
    );

    // Save addresses
    debugPrint('Saving home address...');
    final homeResult = await addressService.saveAddress(homeAddress);
    debugPrint('Home address saved: $homeResult');

    debugPrint('Saving work address...');
    final workResult = await addressService.saveAddress(workAddress);
    debugPrint('Work address saved: $workResult');

    // Get all addresses
    debugPrint('Getting all saved addresses...');
    final savedAddresses = await addressService.getSavedAddresses();
    debugPrint('Found ${savedAddresses.length} saved addresses:');
    for (final address in savedAddresses) {
      debugPrint('  - ${address.title}: ${address.fullAddress}');
    }

    // Get default address
    debugPrint('Getting default address...');
    final defaultAddress = await addressService.getDefaultAddress();
    if (defaultAddress != null) {
      debugPrint('Default address: ${defaultAddress.title}');
    } else {
      debugPrint('No default address found');
    }

    // Search addresses
    debugPrint('Searching for "home"...');
    final searchResults = await addressService.searchAddresses('home');
    debugPrint('Found ${searchResults.length} matching addresses:');
    for (final address in searchResults) {
      debugPrint('  - ${address.title}: ${address.fullAddress}');
    }
  }

  static void demonstrateDialogFlow() {
    debugPrint('=== Dialog Flow Demo ===');
    debugPrint('1. User clicks "Add pickup details" button');
    debugPrint('2. System checks for saved addresses');
    debugPrint('3a. If addresses exist: Show address selection dialog');
    debugPrint('    - User can select existing address');
    debugPrint('    - User can edit existing address');
    debugPrint('    - User can add new address');
    debugPrint('3b. If no addresses exist: Open map picker directly');
    debugPrint('4. Map picker allows user to:');
    debugPrint('    - Select location on map');
    debugPrint('    - Enter address details');
    debugPrint('    - Set address type (home/work/other)');
    debugPrint('    - Add landmark and contact info');
    debugPrint('    - Set as default address');
    debugPrint('5. Address is saved and selected for pickup/delivery');
  }

  static Future<void> runFullDemo() async {
    debugPrint('🚀 Starting Address Functionality Demo...\n');
    
    demonstrateAddressModel();
    debugPrint('');
    
    await demonstrateAddressService();
    debugPrint('');
    
    demonstrateDialogFlow();
    
    debugPrint('\n✅ Address Functionality Demo Complete!');
  }
}
