class PackageItemModel {
  final String id;
  final int itemNumber;
  final PackageDimension dimension;
  final String description;
  final String contents;
  final double value;

  PackageItemModel({
    required this.id,
    required this.itemNumber,
    required this.dimension,
    this.description = '',
    this.contents = '',
    this.value = 0.0,
  });

  // Create copy with updated values
  PackageItemModel copyWith({
    String? id,
    int? itemNumber,
    PackageDimension? dimension,
    String? description,
    String? contents,
    double? value,
  }) {
    return PackageItemModel(
      id: id ?? this.id,
      itemNumber: itemNumber ?? this.itemNumber,
      dimension: dimension ?? this.dimension,
      description: description ?? this.description,
      contents: contents ?? this.contents,
      value: value ?? this.value,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itemNumber': itemNumber,
      'dimension': dimension.toJson(),
      'description': description,
      'contents': contents,
      'value': value,
    };
  }

  // Create from JSON
  factory PackageItemModel.fromJson(Map<String, dynamic> json) {
    return PackageItemModel(
      id: json['id'] ?? '',
      itemNumber: json['itemNumber'] ?? 1,
      dimension: PackageDimension.fromJson(json['dimension'] ?? {}),
      description: json['description'] ?? '',
      contents: json['contents'] ?? '',
      value: (json['value'] ?? 0.0).toDouble(),
    );
  }

  @override
  String toString() {
    return 'PackageItemModel(id: $id, itemNumber: $itemNumber, dimension: ${dimension.name}, value: $value)';
  }
}

class PackageDimension {
  final String id;
  final String name;
  final String description;
  final String weightLimit;
  final String icon;
  final bool isSelected;

  PackageDimension({
    required this.id,
    required this.name,
    required this.description,
    required this.weightLimit,
    required this.icon,
    this.isSelected = false,
  });

  // Create copy with updated selection
  PackageDimension copyWith({
    String? id,
    String? name,
    String? description,
    String? weightLimit,
    String? icon,
    bool? isSelected,
  }) {
    return PackageDimension(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      weightLimit: weightLimit ?? this.weightLimit,
      icon: icon ?? this.icon,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'weightLimit': weightLimit,
      'icon': icon,
      'isSelected': isSelected,
    };
  }

  // Create from JSON
  factory PackageDimension.fromJson(Map<String, dynamic> json) {
    return PackageDimension(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      weightLimit: json['weightLimit'] ?? '',
      icon: json['icon'] ?? '',
      isSelected: json['isSelected'] ?? false,
    );
  }

  // Predefined dimensions
  static List<PackageDimension> getDefaultDimensions() {
    return [
      PackageDimension(
        id: 'xs',
        name: 'XS',
        description: '(Parcel up to 500gm)',
        weightLimit: '500gm',
        icon: '📦',
      ),
      PackageDimension(
        id: 'small',
        name: 'Small',
        description: '(Parcel up to 2Kg)',
        weightLimit: '2Kg',
        icon: '📦',
      ),
      PackageDimension(
        id: 'medium',
        name: 'Medium',
        description: '(Parcel up to 5Kg)',
        weightLimit: '5Kg',
        icon: '📦',
      ),
      PackageDimension(
        id: 'large',
        name: 'Large',
        description: '(Parcel up to 20Kg)',
        weightLimit: '20Kg',
        icon: '📦',
      ),
    ];
  }

  @override
  String toString() {
    return 'PackageDimension(id: $id, name: $name, description: $description)';
  }
}
