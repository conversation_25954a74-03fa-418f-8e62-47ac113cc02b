class AddressModel {
  final String? id;
  final String title;
  final String fullAddress;
  final String? landmark;
  final double latitude;
  final double longitude;
  final String? addressType; // 'home', 'work', 'other'
  final String? contactName;
  final String? contactPhone;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  AddressModel({
    this.id,
    required this.title,
    required this.fullAddress,
    this.landmark,
    required this.latitude,
    required this.longitude,
    this.addressType,
    this.contactName,
    this.contactPhone,
    this.isDefault = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'fullAddress': fullAddress,
      'landmark': landmark,
      'latitude': latitude,
      'longitude': longitude,
      'addressType': addressType,
      'contactName': contactName,
      'contactPhone': contactPhone,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Create from JSON
  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      id: json['id'],
      title: json['title'] ?? '',
      fullAddress: json['fullAddress'] ?? '',
      landmark: json['landmark'],
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      addressType: json['addressType'],
      contactName: json['contactName'],
      contactPhone: json['contactPhone'],
      isDefault: json['isDefault'] ?? false,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : DateTime.now(),
    );
  }

  // Copy with method for updates
  AddressModel copyWith({
    String? id,
    String? title,
    String? fullAddress,
    String? landmark,
    double? latitude,
    double? longitude,
    String? addressType,
    String? contactName,
    String? contactPhone,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AddressModel(
      id: id ?? this.id,
      title: title ?? this.title,
      fullAddress: fullAddress ?? this.fullAddress,
      landmark: landmark ?? this.landmark,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      addressType: addressType ?? this.addressType,
      contactName: contactName ?? this.contactName,
      contactPhone: contactPhone ?? this.contactPhone,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'AddressModel(id: $id, title: $title, fullAddress: $fullAddress, latitude: $latitude, longitude: $longitude)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AddressModel &&
        other.id == id &&
        other.title == title &&
        other.fullAddress == fullAddress &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        fullAddress.hashCode ^
        latitude.hashCode ^
        longitude.hashCode;
  }

  // Get formatted address for display
  String get displayAddress {
    if (landmark != null && landmark!.isNotEmpty) {
      return '$fullAddress\nNear $landmark';
    }
    return fullAddress;
  }

  // Get address type icon
  String get typeIcon {
    switch (addressType?.toLowerCase()) {
      case 'home':
        return '🏠';
      case 'work':
        return '🏢';
      default:
        return '📍';
    }
  }
}
