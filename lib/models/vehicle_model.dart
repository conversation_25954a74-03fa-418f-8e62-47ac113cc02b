class VehicleModel {
  final String id;
  final String name;
  final String description;
  final String imageAsset;
  final double capacity; // in tons
  final String dimensions; // e.g., "10ft x 6ft"
  final double pricePerKm;
  final double basePrice;
  final bool isSelected;

  VehicleModel({
    required this.id,
    required this.name,
    required this.description,
    required this.imageAsset,
    required this.capacity,
    required this.dimensions,
    required this.pricePerKm,
    required this.basePrice,
    this.isSelected = false,
  });

  // Create copy with updated values
  VehicleModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageAsset,
    double? capacity,
    String? dimensions,
    double? pricePerKm,
    double? basePrice,
    bool? isSelected,
  }) {
    return VehicleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageAsset: imageAsset ?? this.imageAsset,
      capacity: capacity ?? this.capacity,
      dimensions: dimensions ?? this.dimensions,
      pricePerKm: pricePerKm ?? this.pricePerKm,
      basePrice: basePrice ?? this.basePrice,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageAsset': imageAsset,
      'capacity': capacity,
      'dimensions': dimensions,
      'pricePerKm': pricePerKm,
      'basePrice': basePrice,
      'isSelected': isSelected,
    };
  }

  // Create from JSON
  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      imageAsset: json['imageAsset'] ?? '',
      capacity: (json['capacity'] ?? 0.0).toDouble(),
      dimensions: json['dimensions'] ?? '',
      pricePerKm: (json['pricePerKm'] ?? 0.0).toDouble(),
      basePrice: (json['basePrice'] ?? 0.0).toDouble(),
      isSelected: json['isSelected'] ?? false,
    );
  }

  // Get default truck vehicles
  static List<VehicleModel> getDefaultTrucks() {
    return [
      VehicleModel(
        id: 'truck_mini',
        name: 'Mini Truck',
        description: 'Perfect for small loads',
        imageAsset: 'assets/images/truck_mini.png',
        capacity: 1.0,
        dimensions: '8ft x 5ft',
        pricePerKm: 15.0,
        basePrice: 500.0,
        isSelected: true, // Default selection
      ),
      VehicleModel(
        id: 'truck_small',
        name: 'Small Truck',
        description: 'Ideal for medium loads',
        imageAsset: 'assets/images/truck_small.png',
        capacity: 2.0,
        dimensions: '10ft x 6ft',
        pricePerKm: 20.0,
        basePrice: 750.0,
      ),
      VehicleModel(
        id: 'truck_medium',
        name: 'Medium Truck',
        description: 'Great for large loads',
        imageAsset: 'assets/images/truck_medium.png',
        capacity: 3.5,
        dimensions: '12ft x 7ft',
        pricePerKm: 25.0,
        basePrice: 1000.0,
      ),
      VehicleModel(
        id: 'truck_large',
        name: 'Large Truck',
        description: 'Best for heavy loads',
        imageAsset: 'assets/images/truck_large.png',
        capacity: 5.0,
        dimensions: '14ft x 8ft',
        pricePerKm: 30.0,
        basePrice: 1500.0,
      ),
    ];
  }

  @override
  String toString() {
    return 'VehicleModel(id: $id, name: $name, capacity: ${capacity}T, price: ₹$pricePerKm/km)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VehicleModel &&
        other.id == id &&
        other.name == name &&
        other.capacity == capacity;
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ capacity.hashCode;
  }

  // Get formatted capacity
  String get formattedCapacity {
    if (capacity == capacity.toInt()) {
      return '${capacity.toInt()}T';
    }
    return '${capacity}T';
  }

  // Get formatted price
  String get formattedPricePerKm {
    return '₹${pricePerKm.toStringAsFixed(0)}/km';
  }

  // Get formatted base price
  String get formattedBasePrice {
    return '₹${basePrice.toStringAsFixed(0)}';
  }
}
