import 'package:flutter/material.dart';
import '../../../common/app_colors.dart';

class AllIndiaReviewStep extends StatelessWidget {
  final dynamic viewModel;
  const AllIndiaReviewStep({Key? key, required this.viewModel}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Mock data for demonstration
    String serviceType = viewModel.serviceType ?? 'Express';
    int estimatedDays = viewModel.estimatedDays ?? 2;
    String trackingIdPreview = viewModel.trackingIdPreview ?? 'WKT123456';
    bool codAvailable = viewModel.codAvailable ?? true;
    bool codSelected = viewModel.codSelected ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Review & Confirm',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Service Type:', style: TextStyle(fontSize: 16, color: Colors.black87)),
            Text(serviceType, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Estimated Delivery:', style: TextStyle(fontSize: 16, color: Colors.black87)),
            Text('$estimatedDays days', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Tracking ID:', style: TextStyle(fontSize: 16, color: Colors.black87)),
            Text(trackingIdPreview, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 24),
        if (codAvailable) ...[
          Row(
            children: [
              Checkbox(
                value: codSelected,
                onChanged: (val) => viewModel.setCodSelected(val),
                activeColor: AppColors.borderColor,
              ),
              const Text('Cash on Delivery (COD) available'),
            ],
          ),
        ],
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: viewModel.onConfirm,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.buttonBackground,
              foregroundColor: AppColors.buttonText,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Confirm & Pay',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ],
    );
  }
} 