import 'dart:async';
import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../app/app.locator.dart';
import '../../../app/app.router.dart';
import '../../../services/auth_service.dart';
import '../../../services/logging_service.dart';

class OtpVerificationViewModel extends BaseViewModel {
  final String phoneNumber;

  final _navigationService = locator<NavigationService>();
  final _snackbarService = locator<SnackbarService>();
  final _authService = locator<AuthService>();
  final _logger = locator<LoggingService>();

  final pinController = TextEditingController();

  bool _isResending = false;
  bool get isResending => _isResending;

  int _resendTimer = 30;
  int get resendTimer => _resendTimer;

  Timer? _timer;

  OtpVerificationViewModel(this.phoneNumber);

  @override
  void dispose() {
    pinController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void startResendTimer() {
    _resendTimer = 30;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendTimer > 0) {
        _resendTimer--;
        notifyListeners();
      } else {
        timer.cancel();
      }
    });
  }

  String formatPhoneNumber(String phoneNumber) {
    // Format phone number for display (e.g., +91 98765 43210)
    if (phoneNumber.startsWith('+91')) {
      String number = phoneNumber.substring(3);
      if (number.length == 10) {
        return '+91 ${number.substring(0, 5)} ${number.substring(5)}';
      }
    }
    return phoneNumber;
  }

  Future<void> verifyOTP() async {
    if (pinController.text.length != 6) {
      _snackbarService.showSnackbar(
        message: 'Please enter a valid 6-digit OTP',
      );
      return;
    }

    setBusy(true);
    _logger.logAuth('Verifying OTP', pinController.text);

    try {
      await _authService.verifyOTP(pinController.text);

      _logger.logNavigation('OtpVerification', 'MainNavigation');
      // Navigate to main navigation screen on successful verification
      await _navigationService.clearStackAndShow(Routes.mainNavigationView);
    } catch (e) {
      _logger.error('OTP verification failed', e);
      _snackbarService.showSnackbar(
        message: 'Verification failed: ${e.toString()}',
      );
      pinController.clear();
    } finally {
      setBusy(false);
    }
  }

  Future<void> resendOTP() async {
    _isResending = true;
    notifyListeners();
    _logger.logAuth('Resending OTP', phoneNumber);

    try {
      await _authService.resendOTP(phoneNumber);
      startResendTimer();

      _snackbarService.showSnackbar(
        message: 'OTP sent successfully',
      );
    } catch (e) {
      _logger.error('Failed to resend OTP', e);
      String errorMessage = 'Failed to resend OTP';

      // Handle specific Firebase errors
      if (e.toString().contains('unusual activity')) {
        errorMessage = 'Too many requests. Please try again later or use a test phone number.';
      } else if (e.toString().contains('reCAPTCHA')) {
        errorMessage = 'CAPTCHA verification required. Please add SHA-1 fingerprint to Firebase.';
      } else if (e.toString().contains('invalid-phone-number')) {
        errorMessage = 'Invalid phone number format.';
      } else if (e.toString().contains('quota-exceeded')) {
        errorMessage = 'SMS quota exceeded. Please try again later.';
      }

      _snackbarService.showSnackbar(
        message: errorMessage,
        duration: const Duration(seconds: 5),
      );
    } finally {
      _isResending = false;
      notifyListeners();
    }
  }
}
