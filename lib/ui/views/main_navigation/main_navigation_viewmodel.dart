import 'package:stacked/stacked.dart';

class MainNavigationViewModel extends BaseViewModel {
  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  void setIndex(int index) {
    _currentIndex = index;
    rebuildUi();
  }

  // Navigation helper methods
  void goToHome() => setIndex(0);
  void goToOrder() => setIndex(1);
  void goToExplore() => setIndex(2);
  void goToAccounts() => setIndex(3);
}
