import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import '../home/<USER>';
import '../order/order_view.dart';
import '../explore/explore_view.dart';
import '../accounts/accounts_view.dart';
import 'main_navigation_viewmodel.dart';
import '../../common/app_colors.dart';

class MainNavigationView extends StackedView<MainNavigationViewModel> {
  const MainNavigationView({super.key});

  @override
  Widget builder(
    BuildContext context,
    MainNavigationViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      body: IndexedStack(
        index: viewModel.currentIndex,
        children: const [
          HomeView(),
          OrderView(),
          ExploreView(),
          AccountsView(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.whiteBackground,
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowColor,
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: viewModel.currentIndex,
          onTap: viewModel.setIndex,
          type: BottomNavigationBarType.fixed,
          backgroundColor: AppColors.whiteBackground,
          selectedItemColor: AppColors.borderColor, // Dark grey black
          unselectedItemColor: AppColors.secondaryText,
          selectedLabelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 11,
            fontWeight: FontWeight.w500,
          ),
          elevation: 0,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.shopping_bag_outlined),
              activeIcon: Icon(Icons.shopping_bag),
              label: 'Order',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.explore_outlined),
              activeIcon: Icon(Icons.explore),
              label: 'Explore',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person_outline),
              activeIcon: Icon(Icons.person),
              label: 'Accounts',
            ),
          ],
        ),
      ),
    );
  }

  @override
  MainNavigationViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      MainNavigationViewModel();
}
