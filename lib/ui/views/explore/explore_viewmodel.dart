import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

class ExploreViewModel extends BaseViewModel {
  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> _popularServices = [];
  List<Map<String, dynamic>> _allServices = [];

  List<Map<String, dynamic>> get categories => _categories;
  List<Map<String, dynamic>> get popularServices => _popularServices;
  List<Map<String, dynamic>> get allServices => _allServices;

  Future<void> loadServices() async {
    setBusy(true);

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 800));

      // Mock categories data
      _categories = [
        {
          'name': 'Transport',
          'icon': Icons.local_shipping,
          'color': const Color(0xFFE65100),
        },
        {
          'name': 'Delivery',
          'icon': Icons.local_post_office,
          'color': const Color(0xFFD32F2F),
        },
        {
          'name': 'Moving',
          'icon': Icons.home_work,
          'color': const Color(0xFF7B1FA2),
        },
        {
          'name': 'Vehicle',
          'icon': Icons.directions_car,
          'color': const Color(0xFF1565C0),
        },
      ];

      // Mock popular services data
      _popularServices = [
        {
          'name': 'Truck Booking',
          'description': 'Heavy goods transport',
          'icon': Icons.local_shipping,
          'gradient': [const Color(0xFFE65100), const Color(0xFFFF9800)],
        },
        {
          'name': 'Local Parcel',
          'description': 'Quick local delivery',
          'icon': Icons.local_post_office,
          'gradient': [const Color(0xFFD32F2F), const Color(0xFFFF5722)],
        },
        {
          'name': 'Packers & Movers',
          'description': 'Complete moving solution',
          'icon': Icons.home_work,
          'gradient': [const Color(0xFF7B1FA2), const Color(0xFF9C27B0)],
        },
      ];

      // Mock all services data
      _allServices = [
        {
          'name': 'Truck Booking',
          'description': 'Book trucks for heavy goods transportation',
          'icon': Icons.local_shipping,
          'color': const Color(0xFFE65100),
        },
        {
          'name': 'Car Booking',
          'description': 'Personal vehicle booking service',
          'icon': Icons.directions_car,
          'color': const Color(0xFF1565C0),
        },
        {
          'name': 'Local Parcel',
          'description': 'Fast local parcel delivery service',
          'icon': Icons.local_post_office,
          'color': const Color(0xFFD32F2F),
        },
        {
          'name': 'All India Parcel',
          'description': 'Nationwide parcel delivery service',
          'icon': Icons.public,
          'color': const Color(0xFF7B1FA2),
        },
        {
          'name': 'Book Vehicle',
          'description': 'Various vehicle booking options',
          'icon': Icons.airport_shuttle,
          'color': const Color(0xFF2E7D32),
        },
        {
          'name': 'Packers & Movers',
          'description': 'Professional packing and moving services',
          'icon': Icons.home_work,
          'color': const Color(0xFFFF6F00),
        },
        {
          'name': 'Express Delivery',
          'description': 'Same day delivery service',
          'icon': Icons.flash_on,
          'color': const Color(0xFFFF5722),
        },
        {
          'name': 'Bulk Transport',
          'description': 'Large quantity goods transport',
          'icon': Icons.inventory,
          'color': const Color(0xFF795548),
        },
      ];

      rebuildUi();
    } catch (e) {
      // Handle error
      debugPrint('Error loading services: $e');
    } finally {
      setBusy(false);
    }
  }

  Future<void> refreshServices() async {
    await loadServices();
  }
}
