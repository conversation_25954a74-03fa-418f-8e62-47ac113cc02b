import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import 'accounts_viewmodel.dart';
import '../../common/app_colors.dart';

class AccountsView extends StackedView<AccountsViewModel> {
  const AccountsView({super.key});

  @override
  Widget builder(
    BuildContext context,
    AccountsViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      appBar: AppBar(
        title: const Text(
          'Account',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement settings
              debugPrint('Settings pressed');
            },
            icon: const Icon(
              Icons.settings_outlined,
              color: Colors.black87,
            ),
          ),
        ],
      ),
      body: viewModel.isBusy
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF0504aa),
              ),
            )
          : SingleChildScrollView(
              child: Column(
                children: [
                  _buildProfileSection(viewModel),
                  _buildMenuSection(viewModel),
                ],
              ),
            ),
    );
  }

  Widget _buildProfileSection(AccountsViewModel viewModel) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.buttonBackground,
            AppColors.buttonBackground.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 3,
              ),
            ),
            child: const Icon(
              Icons.person,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            viewModel.userProfile?['user']?['phoneNumber'] ?? 'User',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.buttonText, // Orange yellow
            ),
          ),
          const SizedBox(height: 4),
          Text(
            viewModel.userProfile?['user']?['email'] ?? '<EMAIL>',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.buttonText.withValues(alpha: 0.8), // Orange yellow with transparency
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('Orders', '12'),
              Container(
                width: 1,
                height: 30,
                color: Colors.white.withValues(alpha: 0.3),
              ),
              _buildStatItem('Saved', '₹2,450'),
              Container(
                width: 1,
                height: 30,
                color: Colors.white.withValues(alpha: 0.3),
              ),
              _buildStatItem('Rating', '4.8'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.buttonText, // Orange yellow
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.buttonText.withValues(alpha: 0.8), // Orange yellow with transparency
          ),
        ),
      ],
    );
  }

  Widget _buildMenuSection(AccountsViewModel viewModel) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildMenuGroup('Account', [
            _buildMenuItem(
              Icons.person_outline,
              'Edit Profile',
              'Update your personal information',
              () => debugPrint('Edit Profile tapped'),
            ),
            _buildMenuItem(
              Icons.location_on_outlined,
              'Addresses',
              'Manage your saved addresses',
              () => debugPrint('Addresses tapped'),
            ),
            _buildMenuItem(
              Icons.payment_outlined,
              'Payment Methods',
              'Manage your payment options',
              () => debugPrint('Payment Methods tapped'),
            ),
          ]),
          const SizedBox(height: 24),
          _buildMenuGroup('Support', [
            _buildMenuItem(
              Icons.help_outline,
              'Help & Support',
              'Get help with your orders',
              () => debugPrint('Help & Support tapped'),
            ),
            _buildMenuItem(
              Icons.feedback_outlined,
              'Feedback',
              'Share your experience with us',
              () => debugPrint('Feedback tapped'),
            ),
            _buildMenuItem(
              Icons.info_outline,
              'About',
              'Learn more about Winget Express',
              () => debugPrint('About tapped'),
            ),
          ]),
          const SizedBox(height: 24),
          _buildMenuGroup('Settings', [
            _buildMenuItem(
              Icons.notifications_outlined,
              'Notifications',
              'Manage your notification preferences',
              () => debugPrint('Notifications tapped'),
            ),
            _buildMenuItem(
              Icons.security_outlined,
              'Privacy & Security',
              'Manage your privacy settings',
              () => debugPrint('Privacy & Security tapped'),
            ),
            _buildMenuItem(
              Icons.logout,
              'Sign Out',
              'Sign out of your account',
              () => viewModel.signOut(),
              isDestructive: true,
            ),
          ]),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildMenuGroup(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(children: items),
        ),
      ],
    );
  }

  Widget _buildMenuItem(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isDestructive
              ? Colors.red.withValues(alpha: 0.1)
              : const Color(0xFF0504aa).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(
          icon,
          color: isDestructive ? Colors.red : const Color(0xFF0504aa),
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: isDestructive ? Colors.red : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey[400],
      ),
      onTap: onTap,
    );
  }

  @override
  AccountsViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      AccountsViewModel();

  @override
  void onViewModelReady(AccountsViewModel viewModel) {
    viewModel.loadUserProfile();
    super.onViewModelReady(viewModel);
  }
}
