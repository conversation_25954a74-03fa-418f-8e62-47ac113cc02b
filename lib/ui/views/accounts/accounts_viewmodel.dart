import 'package:flutter/foundation.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../app/app.locator.dart';
import '../../../app/app.router.dart';
import '../../../services/auth_service.dart';

class AccountsViewModel extends BaseViewModel {
  final _authService = locator<AuthService>();
  final _navigationService = locator<NavigationService>();

  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? get userProfile => _userProfile;

  Future<void> loadUserProfile() async {
    setBusy(true);

    try {
      _userProfile = await _authService.getUserProfile();
      rebuildUi();
    } catch (e) {
      // Handle error
      debugPrint('Error loading user profile: $e');
    } finally {
      setBusy(false);
    }
  }

  Future<void> signOut() async {
    setBusy(true);

    try {
      await _authService.signOut();
      await _navigationService.clearStackAndShow(Routes.signInView);
    } catch (e) {
      // Handle error
      debugPrint('Error signing out: $e');
    } finally {
      setBusy(false);
    }
  }

  Future<void> refreshProfile() async {
    await loadUserProfile();
  }
}
