import 'package:flutter/foundation.dart';
import 'package:stacked/stacked.dart';

class OrderViewModel extends BaseViewModel {
  List<Map<String, dynamic>> _orders = [];
  List<Map<String, dynamic>> get orders => _orders;

  Future<void> loadOrders() async {
    setBusy(true);

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 1000));

      // Mock data - replace with actual API call
      _orders = [
        {
          'id': '12345',
          'service': 'Truck Booking',
          'status': 'Completed',
          'date': '2024-01-15',
          'amount': '2,500',
        },
        {
          'id': '12346',
          'service': 'Local Parcel',
          'status': 'In Progress',
          'date': '2024-01-20',
          'amount': '150',
        },
        {
          'id': '12347',
          'service': 'Packers & Movers',
          'status': 'Pending',
          'date': '2024-01-22',
          'amount': '8,500',
        },
      ];

      rebuildUi();
    } catch (e) {
      // Handle error
      debugPrint('Error loading orders: $e');
    } finally {
      setBusy(false);
    }
  }

  Future<void> refreshOrders() async {
    await loadOrders();
  }
}
