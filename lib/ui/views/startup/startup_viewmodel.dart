import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../app/app.locator.dart';
import '../../../app/app.router.dart';
import '../../../services/auth_service.dart';
import '../../../services/logging_service.dart';

class StartupViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _authService = locator<AuthService>();
  final _logger = locator<LoggingService>();

  Future<void> runStartupLogic() async {
    try {
      _logger.logApp('Initializing AuthService...');
      // Initialize Firebase and AuthService
      await _authService.initialize();

      // Check if user is already authenticated
      if (_authService.isAuthenticated && _authService.currentUser != null) {
        _logger.logNavigation('Startup', 'MainNavigation');
        // User is logged in, navigate to main navigation
        await _navigationService.replaceWithMainNavigationView();
      } else {
        _logger.logNavigation('Startup', 'SignIn');
        // User is not logged in, navigate to sign in
        await _navigationService.replaceWithSignInView();
      }
    } catch (e) {
      _logger.error('Startup error, navigating to sign in', e);
      // If there's an error, navigate to sign in
      await _navigationService.replaceWithSignInView();
    }
  }

  Future<void> initialise() async {
    await runStartupLogic();
  }
}
