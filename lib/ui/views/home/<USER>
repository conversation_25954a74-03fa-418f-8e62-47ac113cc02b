import 'package:firebase_auth/firebase_auth.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../app/app.locator.dart';
import '../../../app/app.router.dart';
import '../../../services/auth_service.dart';
import '../../../services/logging_service.dart';

class HomeViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _snackbarService = locator<SnackbarService>();
  final _authService = locator<AuthService>();
  final _logger = locator<LoggingService>();

  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? get userProfile => _userProfile;

  User? get currentUser => _authService.currentUser;

  Future<void> loadUserProfile() async {
    setBusy(true);
    _logger.logAuth('Loading user profile');

    try {
      final profile = await _authService.getUserProfile();
      _userProfile = profile;
      notifyListeners();
    } catch (e) {
      // If profile loading fails, it might be due to token issues
      // You can handle this by refreshing token or showing error
      _logger.error('Failed to load profile', e);
    } finally {
      setBusy(false);
    }
  }

  Future<void> refreshProfile() async {
    await loadUserProfile();

    _snackbarService.showSnackbar(
      message: 'Profile refreshed',
    );
  }

  Future<void> signOut() async {
    try {
      _logger.logAuth('Signing out');
      await _authService.signOut();
      _logger.logNavigation('Home', 'SignIn');
      await _navigationService.clearStackAndShow(Routes.signInView);
    } catch (e) {
      _logger.error('Sign out failed', e);
      _snackbarService.showSnackbar(
        message: 'Sign out failed: ${e.toString()}',
      );
    }
  }
}
