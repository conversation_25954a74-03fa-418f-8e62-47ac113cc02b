import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../models/address_model.dart';
import '../../../services/address_service.dart';
import '../../../services/location_service.dart';
import '../../../services/places_service.dart';
import 'map_styles.dart';
import 'dart:async';

class MapPickerViewModel extends BaseViewModel {
  final AddressService _addressService = AddressService();
  final LocationService _locationService = LocationService();
  final PlacesService _placesService = PlacesService();

  // Google Maps properties
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};

  // Map and location properties
  double _latitude = 28.6139; // Default to Delhi
  double _longitude = 77.2090;
  String _fullAddress = '';
  String _landmark = '';
  bool _isLoadingAddress = false;

  // Form properties
  final TextEditingController titleController = TextEditingController();
  final TextEditingController landmarkController = TextEditingController();
  final TextEditingController contactNameController = TextEditingController();
  final TextEditingController contactPhoneController = TextEditingController();
  final TextEditingController searchController = TextEditingController();
  String _selectedAddressType = 'home';
  bool _setAsDefault = false;

  // Search properties
  List<PlacePrediction> _searchResults = [];
  bool _showSearchResults = false;
  bool _isSearching = false;
  Timer? _searchTimer;

  // Map style properties
  int _currentMapStyleIndex = 0;
  final List<String> _mapStyles = [
    MapStyles.lightTheme,
    MapStyles.blueTheme,
    MapStyles.darkTheme,
  ];

  // Editing mode
  bool _isEditMode = false;
  AddressModel? _editingAddress;

  // Getters
  double get latitude => _latitude;
  double get longitude => _longitude;
  String get fullAddress => _fullAddress;
  String get landmark => _landmark;
  bool get isLoadingAddress => _isLoadingAddress;
  String get selectedAddressType => _selectedAddressType;
  bool get setAsDefault => _setAsDefault;
  bool get isEditMode => _isEditMode;
  AddressModel? get editingAddress => _editingAddress;
  Set<Marker> get markers => _markers;
  List<PlacePrediction> get searchResults => _searchResults;
  bool get showSearchResults => _showSearchResults;
  bool get isSearching => _isSearching;
  String get mapStyle => _mapStyles[_currentMapStyleIndex];

  // Address type options
  final List<Map<String, String>> addressTypes = [
    {'value': 'home', 'label': 'Home', 'icon': '🏠'},
    {'value': 'work', 'label': 'Work', 'icon': '🏢'},
    {'value': 'other', 'label': 'Other', 'icon': '📍'},
  ];

  // Initialize for editing
  void initializeForEdit(AddressModel address) {
    _isEditMode = true;
    _editingAddress = address;

    _latitude = address.latitude;
    _longitude = address.longitude;
    _fullAddress = address.fullAddress;
    _landmark = address.landmark ?? '';

    titleController.text = address.title;
    landmarkController.text = address.landmark ?? '';
    contactNameController.text = address.contactName ?? '';
    contactPhoneController.text = address.contactPhone ?? '';
    _selectedAddressType = address.addressType ?? 'home';
    _setAsDefault = address.isDefault;

    _updateMarker();
    rebuildUi();
  }

  // Update location from map
  void updateLocation(double lat, double lng) {
    _latitude = lat;
    _longitude = lng;
    _updateMarker();
    _reverseGeocode(lat, lng);
    rebuildUi();
  }

  // Google Maps callback when map is created
  void onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _updateMarker();
    debugPrint('Google Map created successfully');
  }

  // Handle map tap to select location
  void onMapTapped(LatLng position) {
    debugPrint('Map tapped at: ${position.latitude}, ${position.longitude}');
    updateLocation(position.latitude, position.longitude);
  }

  // Update marker position
  void _updateMarker() {
    _markers = {
      Marker(
        markerId: const MarkerId('selected_location'),
        position: LatLng(_latitude, _longitude),
        infoWindow: InfoWindow(
          title: 'Selected Location',
          snippet: _fullAddress.isNotEmpty ? _fullAddress : 'Tap to select address',
        ),
      ),
    };
  }

  // Reverse geocode to get address
  Future<void> _reverseGeocode(double lat, double lng) async {
    _isLoadingAddress = true;
    rebuildUi();

    try {
      // Try Places API first for better results
      final address = await _placesService.reverseGeocode(lat, lng);
      _fullAddress = address;

      // Auto-fill title if empty
      if (titleController.text.isEmpty) {
        titleController.text = _selectedAddressType == 'home'
            ? 'Home'
            : _selectedAddressType == 'work'
                ? 'Work'
                : 'My Location';
      }

      // Update marker with new address
      _updateMarker();
    } catch (e) {
      debugPrint('Error in reverse geocoding: $e');
      // Fallback to location service
      try {
        final addressResult = await _locationService.getAddressFromCoordinates(
          latitude: lat,
          longitude: lng,
        );
        _fullAddress = addressResult.formattedAddress;
      } catch (e2) {
        debugPrint('Fallback reverse geocoding also failed: $e2');
        _fullAddress = 'Location at ${lat.toStringAsFixed(4)}, ${lng.toStringAsFixed(4)}';
      }
    } finally {
      _isLoadingAddress = false;
      rebuildUi();
    }
  }

  // Set address type
  void setAddressType(String type) {
    _selectedAddressType = type;

    // Auto-update title if it matches the previous type
    if (titleController.text.toLowerCase() == 'home' ||
        titleController.text.toLowerCase() == 'work' ||
        titleController.text.toLowerCase() == 'my location') {
      titleController.text = type == 'home'
          ? 'Home'
          : type == 'work'
              ? 'Work'
              : 'My Location';
    }

    rebuildUi();
  }

  // Toggle default setting
  void toggleDefault(bool value) {
    _setAsDefault = value;
    rebuildUi();
  }

  // Validate form
  bool get canSaveAddress {
    return titleController.text.trim().isNotEmpty &&
           _fullAddress.isNotEmpty &&
           !_isLoadingAddress;
  }

  // Save address
  Future<AddressModel?> saveAddress() async {
    if (!canSaveAddress) return null;

    setBusy(true);

    try {
      final address = AddressModel(
        id: _isEditMode ? _editingAddress!.id : null,
        title: titleController.text.trim(),
        fullAddress: _fullAddress,
        landmark: landmarkController.text.trim().isEmpty
            ? null
            : landmarkController.text.trim(),
        latitude: _latitude,
        longitude: _longitude,
        addressType: _selectedAddressType,
        contactName: contactNameController.text.trim().isEmpty
            ? null
            : contactNameController.text.trim(),
        contactPhone: contactPhoneController.text.trim().isEmpty
            ? null
            : contactPhoneController.text.trim(),
        isDefault: _setAsDefault,
        createdAt: _isEditMode ? _editingAddress!.createdAt : null,
      );

      bool success;
      if (_isEditMode) {
        success = await _addressService.updateAddress(address);
      } else {
        success = await _addressService.saveAddress(address);
      }

      if (success) {
        debugPrint('Address ${_isEditMode ? 'updated' : 'saved'} successfully');
        return address;
      } else {
        debugPrint('Failed to ${_isEditMode ? 'update' : 'save'} address');
        return null;
      }
    } catch (e) {
      debugPrint('Error saving address: $e');
      return null;
    } finally {
      setBusy(false);
    }
  }

  // Cancel and go back
  void cancel() {
    final context = StackedService.navigatorKey?.currentContext;
    if (context != null) {
      Navigator.of(context).pop();
    }
  }

  // Get current location
  Future<void> getCurrentLocation() async {
    setBusy(true);

    try {
      final locationResult = await _locationService.getCurrentLocation();
      updateLocation(locationResult.latitude, locationResult.longitude);
      debugPrint('Current location obtained: ${locationResult.latitude}, ${locationResult.longitude}');
    } catch (e) {
      debugPrint('Error getting current location: $e');
      // Fallback to default location (Delhi)
      updateLocation(28.6139, 77.2090);
    } finally {
      setBusy(false);
    }
  }

  // Search functionality
  void onSearchChanged(String query) {
    // Cancel previous search timer
    _searchTimer?.cancel();

    if (query.isEmpty) {
      _clearSearchResults();
      return;
    }

    // Debounce search to avoid too many API calls
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  void onSearchSubmitted(String query) {
    _searchTimer?.cancel();
    if (query.isNotEmpty) {
      _performSearch(query);
    }
  }

  Future<void> _performSearch(String query) async {
    _isSearching = true;
    _showSearchResults = true;
    rebuildUi();

    try {
      final results = await _placesService.searchPlaces(query);
      _searchResults = results;
      debugPrint('Search completed: Found ${results.length} results for "$query"');
    } catch (e) {
      debugPrint('Search error: $e');
      _searchResults = [];
    } finally {
      _isSearching = false;
      rebuildUi();
    }
  }

  void selectSearchResult(PlacePrediction prediction) async {
    debugPrint('Selected place: ${prediction.description}');

    // Clear search
    searchController.text = prediction.mainText;
    _clearSearchResults();

    // Get place details to get coordinates
    try {
      final placeDetails = await _placesService.getPlaceDetails(prediction.placeId);
      if (placeDetails != null) {
        updateLocation(placeDetails.latitude, placeDetails.longitude);

        // Move map camera to selected location
        _mapController?.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(placeDetails.latitude, placeDetails.longitude),
            16.0,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error getting place details: $e');
    }
  }

  void clearSearch() {
    searchController.clear();
    _clearSearchResults();
  }

  void _clearSearchResults() {
    _searchResults = [];
    _showSearchResults = false;
    _isSearching = false;
    rebuildUi();
  }

  // Map style functionality
  void toggleMapStyle() {
    _currentMapStyleIndex = (_currentMapStyleIndex + 1) % _mapStyles.length;
    debugPrint('Map style changed to index: $_currentMapStyleIndex');
    rebuildUi();
  }

  @override
  void dispose() {
    titleController.dispose();
    landmarkController.dispose();
    contactNameController.dispose();
    contactPhoneController.dispose();
    searchController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }
}
