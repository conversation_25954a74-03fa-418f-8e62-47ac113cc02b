import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'map_picker_viewmodel.dart';
import '../../common/app_colors.dart';
import '../../../models/address_model.dart';

class MapPickerView extends StackedView<MapPickerViewModel> {
  final AddressModel? editAddress;

  const MapPickerView({super.key, this.editAddress});

  @override
  Widget builder(
    BuildContext context,
    MapPickerViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: viewModel.cancel,
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.black87,
            size: 24,
          ),
        ),
        title: Text(
          viewModel.isEditMode ? 'Edit Address' : 'Add New Address',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: false,
        actions: [
          IconButton(
            onPressed: viewModel.getCurrentLocation,
            icon: const Icon(
              Icons.my_location,
              color: Colors.black87,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Map Section
          Expanded(
            flex: 2,
            child: _buildMapSection(viewModel),
          ),

          // Address Details Section
          Expanded(
            flex: 3,
            child: _buildAddressDetailsSection(viewModel),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomButton(viewModel),
    );
  }

  Widget _buildMapSection(MapPickerViewModel viewModel) {
    return Container(
      color: Colors.white,
      child: Stack(
        children: [
          // Google Map
          GoogleMap(
            onMapCreated: viewModel.onMapCreated,
            initialCameraPosition: CameraPosition(
              target: LatLng(viewModel.latitude, viewModel.longitude),
              zoom: 16.0,
            ),
            onTap: viewModel.onMapTapped,
            markers: viewModel.markers,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
            style: viewModel.mapStyle, // Custom map styling
          ),

          // Search Bar
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: _buildSearchBar(viewModel),
          ),

          // Search Results
          if (viewModel.showSearchResults && viewModel.searchResults.isNotEmpty)
            Positioned(
              top: 72,
              left: 16,
              right: 16,
              child: _buildSearchResults(viewModel),
            ),

          // Center Pin (only show when not searching)
          if (!viewModel.showSearchResults)
            const Center(
              child: Icon(
                Icons.location_pin,
                size: 40,
                color: Colors.red,
              ),
            ),

          // Map Style Toggle Button
          Positioned(
            bottom: 16,
            right: 16,
            child: FloatingActionButton(
              mini: true,
              backgroundColor: Colors.white,
              foregroundColor: AppColors.borderColor,
              onPressed: viewModel.toggleMapStyle,
              child: const Icon(Icons.layers),
            ),
          ),

          // Loading overlay
          if (viewModel.isLoadingAddress)
            Container(
              color: Colors.black26,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAddressDetailsSection(MapPickerViewModel viewModel) {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Address Display
            if (viewModel.fullAddress.isNotEmpty) ...[
              const Text(
                'Selected Location',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.pageBackground,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.borderColor),
                ),
                child: Text(
                  viewModel.fullAddress,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],

            // Address Title
            const Text(
              'Address Title *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: viewModel.titleController,
              decoration: InputDecoration(
                hintText: 'e.g., Home, Office, etc.',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.borderColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.pickupIconColor),
                ),
              ),
              onChanged: (value) => viewModel.rebuildUi(),
            ),
            const SizedBox(height: 16),

            // Address Type
            const Text(
              'Address Type',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: viewModel.addressTypes.map((type) {
                final isSelected = viewModel.selectedAddressType == type['value'];
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: GestureDetector(
                      onTap: () => viewModel.setAddressType(type['value']!),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected ? AppColors.pickupIconColor : Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSelected ? AppColors.pickupIconColor : AppColors.borderColor,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              type['icon']!,
                              style: const TextStyle(fontSize: 20),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              type['label']!,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: isSelected ? Colors.white : Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // Landmark
            const Text(
              'Landmark (Optional)',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: viewModel.landmarkController,
              decoration: InputDecoration(
                hintText: 'e.g., Near Metro Station',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.borderColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.pickupIconColor),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Contact Details
            const Text(
              'Contact Details (Optional)',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: viewModel.contactNameController,
                    decoration: InputDecoration(
                      hintText: 'Contact Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: AppColors.borderColor),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: AppColors.pickupIconColor),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: viewModel.contactPhoneController,
                    keyboardType: TextInputType.phone,
                    decoration: InputDecoration(
                      hintText: 'Phone Number',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: AppColors.borderColor),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: AppColors.pickupIconColor),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Set as Default
            Row(
              children: [
                Checkbox(
                  value: viewModel.setAsDefault,
                  onChanged: (value) => viewModel.toggleDefault(value ?? false),
                  activeColor: AppColors.pickupIconColor,
                ),
                const Text(
                  'Set as default address',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomButton(MapPickerViewModel viewModel) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(20),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: Builder(
          builder: (context) => ElevatedButton(
            onPressed: viewModel.canSaveAddress && !viewModel.isBusy
                ? () async {
                    final result = await viewModel.saveAddress();
                    if (result != null && context.mounted) {
                      Navigator.of(context).pop(result);
                    }
                  }
                : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.buttonBackground,
            foregroundColor: AppColors.buttonText,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: viewModel.isBusy
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  viewModel.isEditMode ? 'Update Address' : 'Save Address',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar(MapPickerViewModel viewModel) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: viewModel.searchController,
        decoration: InputDecoration(
          hintText: 'Search for places...',
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          suffixIcon: viewModel.isSearching
              ? const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
              : viewModel.searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, color: Colors.grey),
                      onPressed: viewModel.clearSearch,
                    )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: viewModel.onSearchChanged,
        onSubmitted: viewModel.onSearchSubmitted,
      ),
    );
  }

  Widget _buildSearchResults(MapPickerViewModel viewModel) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: viewModel.searchResults.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final prediction = viewModel.searchResults[index];
          return ListTile(
            leading: const Icon(Icons.location_on, color: Colors.grey),
            title: Text(
              prediction.mainText,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              prediction.secondaryText,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            onTap: () => viewModel.selectSearchResult(prediction),
          );
        },
      ),
    );
  }

  @override
  void onViewModelReady(MapPickerViewModel viewModel) {
    if (editAddress != null) {
      viewModel.initializeForEdit(editAddress!);
    }
  }

  @override
  MapPickerViewModel viewModelBuilder(BuildContext context) => MapPickerViewModel();
}
