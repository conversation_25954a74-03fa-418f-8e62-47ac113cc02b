import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import 'cab_booking_viewmodel.dart';
import 'ride_search_view.dart';
import '../../common/app_colors.dart';

class CabBookingView extends StackedView<CabBookingViewModel> {
  const CabBookingView({super.key});

  @override
  Widget builder(
    BuildContext context,
    CabBookingViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground,
      appBar: AppBar(
        title: Text(
          viewModel.currentStep == 0 ? 'Cab Booking' : 'Searching Ride',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
            fontFamily: 'Poppins',
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black87),
        centerTitle: true,
      ),
      body: viewModel.currentStep == 0 
          ? _buildAddressSelectionStep(viewModel)
          : RideSearchView(viewModel: viewModel),
    );
  }

  Widget _buildAddressSelectionStep(CabBookingViewModel viewModel) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Show detailed address view if pickup address is selected
                if (viewModel.hasPickupAddress) ...[
                  _buildDetailedAddressView(viewModel),
                ] else ...[
                  // Show selection interface if pickup address not selected
                  // Pickup Section
                  _buildLocationSection(
                    icon: Icons.keyboard_arrow_up,
                    iconColor: AppColors.pickupIconColor,
                    title: 'PICKUP FROM',
                    buttonText: 'Add pickup location',
                    isAddressSelected: false,
                    onPressed: () => viewModel.handlePickupAddressSelection(),
                  ),

                  const SizedBox(height: 24),

                  // Drop Section
                  _buildLocationSection(
                    icon: Icons.keyboard_arrow_down,
                    iconColor: AppColors.deliveryIconColor,
                    title: 'DROP TO',
                    buttonText: 'Add drop location',
                    isAddressSelected: false,
                    onPressed: () => viewModel.handleDropAddressSelection(),
                  ),
                ],

                // Distance information
                if (viewModel.distanceResult != null) ...[
                  const SizedBox(height: 24),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.lightBlueBackground,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.route,
                              color: AppColors.primaryBlue,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Distance: ${viewModel.distanceResult!.distanceText}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                                fontFamily: 'Poppins',
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Icon(
                              Icons.currency_rupee,
                              color: AppColors.primaryBlue,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Estimated Fare: ₹${viewModel.calculatedFare.toStringAsFixed(0)}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                                fontFamily: 'Poppins',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
        
        // Bottom button
        if (viewModel.hasCompleteAddresses) ...[
          Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: viewModel.proceedToRideSearch,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.buttonBackground,
                  foregroundColor: AppColors.buttonText,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Search Ride',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Poppins',
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Icon(Icons.search, size: 20),
                  ],
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLocationSection({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String buttonText,
    required bool isAddressSelected,
    required VoidCallback onPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadowColor,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                  letterSpacing: 0.5,
                  fontFamily: 'Poppins',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: iconColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: Text(
                buttonText,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Poppins',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedAddressView(CabBookingViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadowColor,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side - Icons and line
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.pickupIconColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_up,
                      color: AppColors.pickupIconColor,
                      size: 20,
                    ),
                  ),
                  Container(
                    width: 2,
                    height: 40,
                    color: AppColors.borderColor.withValues(alpha: 0.3),
                    margin: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.deliveryIconColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.deliveryIconColor,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              // Right side - Address details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Pickup address
                    _buildAddressDetails(
                      title: 'Pickup Location',
                      address: viewModel.pickupAddressDisplay,
                      phone: viewModel.pickupDetails?['contactPhone'] ?? '',
                      onEdit: () => viewModel.handlePickupAddressSelection(),
                    ),
                    const SizedBox(height: 32),
                    // Drop address
                    if (viewModel.hasDropAddress) ...[
                      _buildAddressDetails(
                        title: 'Drop Location',
                        address: viewModel.dropAddressDisplay,
                        phone: viewModel.dropDetails?['contactPhone'] ?? '',
                        onEdit: () => viewModel.handleDropAddressSelection(),
                      ),
                    ] else ...[
                      _buildAddDropButton(viewModel),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddressDetails({
    required String title,
    required String address,
    required String phone,
    required VoidCallback onEdit,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
                letterSpacing: 0.5,
                fontFamily: 'Poppins',
              ),
            ),
            GestureDetector(
              onTap: onEdit,
              child: const Text(
                'Edit',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                  fontFamily: 'Poppins',
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          address,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            height: 1.4,
            fontFamily: 'Poppins',
          ),
          maxLines: null, // Allow unlimited lines for long addresses
          softWrap: true, // Enable text wrapping
        ),
        if (phone.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Phone: $phone',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontFamily: 'Poppins',
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddDropButton(CabBookingViewModel viewModel) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => viewModel.handleDropAddressSelection(),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.deliveryIconColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Add drop details',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            fontFamily: 'Poppins',
          ),
        ),
      ),
    );
  }

  @override
  CabBookingViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      CabBookingViewModel();
}
