import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

import '../../../app/app.locator.dart';
import '../../../app/app.router.dart';
import '../../../services/auth_service.dart';
import '../../../services/logging_service.dart';

class SignInViewModel extends BaseViewModel {
  final _navigationService = locator<NavigationService>();
  final _snackbarService = locator<SnackbarService>();
  final _authService = locator<AuthService>();
  final _logger = locator<LoggingService>();

  final formKey = GlobalKey<FormState>();
  final phoneController = TextEditingController();

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  String _formatPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Add country code if not present (assuming +91 for India)
    if (!cleaned.startsWith('91') && cleaned.length == 10) {
      cleaned = '91$cleaned';
    }

    return '+$cleaned';
  }

  bool _isValidPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Check if it's a valid 10-digit number or 12-digit with country code
    return cleaned.length == 10 || (cleaned.length == 12 && cleaned.startsWith('91'));
  }

  String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your phone number';
    }
    if (!_isValidPhoneNumber(value)) {
      return 'Please enter a valid 10-digit phone number';
    }
    return null;
  }

  Future<void> sendOTP() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    setBusy(true);
    _logger.logAuth('Sending OTP', phoneController.text);

    try {
      final formattedPhone = _formatPhoneNumber(phoneController.text);

      await _authService.authenticateWithPhone(formattedPhone);

      _logger.logNavigation('SignIn', 'OtpVerification');
      // Navigate to OTP verification screen
      await _navigationService.navigateToOtpVerificationView(
        phoneNumber: formattedPhone,
      );
    } catch (e) {
      _logger.error('Failed to send OTP', e);
      String errorMessage = 'Failed to send OTP';

      // Handle specific Firebase errors
      if (e.toString().contains('unusual activity')) {
        errorMessage = 'Too many requests from this device. Please:\n• Wait a few hours and try again\n• Add your number as a test number in Firebase\n• Add SHA-1 fingerprint to Firebase Console';
      } else if (e.toString().contains('reCAPTCHA')) {
        errorMessage = 'CAPTCHA verification failed. Please add SHA-1 fingerprint to Firebase Console.';
      } else if (e.toString().contains('invalid-phone-number')) {
        errorMessage = 'Please enter a valid phone number.';
      } else if (e.toString().contains('quota-exceeded')) {
        errorMessage = 'SMS quota exceeded. Please try again later.';
      }

      _snackbarService.showSnackbar(
        message: errorMessage,
        duration: const Duration(seconds: 6),
      );
    } finally {
      setBusy(false);
    }
  }
}
