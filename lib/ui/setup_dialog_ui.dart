import 'package:stacked_services/stacked_services.dart';
import '../app/app.locator.dart';
import 'dialogs/address_selection_dialog.dart';
import 'dialogs/package_description_dialog.dart';
import 'dialogs/package_contents_dialog.dart';
import 'dialogs/order_success_dialog.dart';

enum DialogType {
  addressSelection,
  packageDescription,
  packageContents,
  orderSuccess,
}

void setupDialogUi() {
  final dialogService = locator<DialogService>();

  final builders = {
    DialogType.addressSelection: (context, request, completer) =>
        AddressSelectionDialog(request: request, completer: completer),
    DialogType.packageDescription: (context, request, completer) =>
        PackageDescriptionDialog(request: request, completer: completer),
    DialogType.packageContents: (context, request, completer) =>
        PackageContentsDialog(request: request, completer: completer),
    DialogType.orderSuccess: (context, request, completer) =>
        OrderSuccessDialog(request: request, completer: completer),
  };

  dialogService.registerCustomDialogBuilders(builders);
}
