import 'package:flutter/material.dart';
import '../../services/distance_service.dart';
import '../common/app_colors.dart';

class DistanceCalculatorWidget extends StatefulWidget {
  final double? pickupLat;
  final double? pickupLng;
  final double? dropLat;
  final double? dropLng;
  final String? pickupAddress;
  final String? dropAddress;
  final Function(DistanceResult)? onDistanceCalculated;

  const DistanceCalculatorWidget({
    super.key,
    this.pickupLat,
    this.pickupLng,
    this.dropLat,
    this.dropLng,
    this.pickupAddress,
    this.dropAddress,
    this.onDistanceCalculated,
  });

  @override
  State<DistanceCalculatorWidget> createState() => _DistanceCalculatorWidgetState();
}

class _DistanceCalculatorWidgetState extends State<DistanceCalculatorWidget> {
  final DistanceService _distanceService = DistanceService();
  DistanceResult? _distanceResult;
  bool _isCalculating = false;

  @override
  void initState() {
    super.initState();
    _calculateDistance();
  }

  @override
  void didUpdateWidget(DistanceCalculatorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_shouldRecalculate(oldWidget)) {
      _calculateDistance();
    }
  }

  bool _shouldRecalculate(DistanceCalculatorWidget oldWidget) {
    return oldWidget.pickupLat != widget.pickupLat ||
           oldWidget.pickupLng != widget.pickupLng ||
           oldWidget.dropLat != widget.dropLat ||
           oldWidget.dropLng != widget.dropLng;
  }

  Future<void> _calculateDistance() async {
    if (!_canCalculateDistance()) return;

    setState(() {
      _isCalculating = true;
      _distanceResult = null;
    });

    try {
      // Calculate road distance using driving mode
      _distanceResult = await _distanceService.calculateRoadDistance(
        originLat: widget.pickupLat!,
        originLng: widget.pickupLng!,
        destinationLat: widget.dropLat!,
        destinationLng: widget.dropLng!,
        travelMode: 'driving',
      );

      if (_distanceResult != null && widget.onDistanceCalculated != null) {
        widget.onDistanceCalculated!(_distanceResult!);
      }
    } catch (e) {
      debugPrint('Error calculating distance: $e');
      // Fallback to straight-line distance
      final straightDistance = DistanceService.calculateStraightLineDistance(
        lat1: widget.pickupLat!,
        lon1: widget.pickupLng!,
        lat2: widget.dropLat!,
        lon2: widget.dropLng!,
      );

      _distanceResult = DistanceResult(
        distance: straightDistance,
        duration: 0, // Not used
        distanceText: '${straightDistance.toStringAsFixed(1)} km (approx)',
        durationText: '', // Not used
        status: 'FALLBACK',
        isApproximate: true,
      );
    } finally {
      setState(() => _isCalculating = false);
    }
  }

  bool _canCalculateDistance() {
    return widget.pickupLat != null &&
           widget.pickupLng != null &&
           widget.dropLat != null &&
           widget.dropLng != null;
  }



  @override
  Widget build(BuildContext context) {
    if (!_canCalculateDistance()) {
      return _buildEmptyState();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 12),
          _buildDistanceInfo(),
          if (widget.pickupAddress != null || widget.dropAddress != null) ...[
            const SizedBox(height: 12),
            _buildAddressInfo(),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Row(
        children: [
          Icon(Icons.route, color: Colors.grey[400], size: 24),
          const SizedBox(width: 12),
          Text(
            'Select pickup and drop locations to calculate distance',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.route,
          color: AppColors.pickupIconColor,
          size: 24,
        ),
        const SizedBox(width: 8),
        const Text(
          'Distance',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const Spacer(),
        if (_isCalculating)
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }



  Widget _buildDistanceInfo() {
    if (_distanceResult == null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          'Calculating distance...',
          style: TextStyle(fontSize: 14, color: Colors.grey),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _distanceResult!.isApproximate
            ? Colors.orange[50]
            : AppColors.pickupIconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _distanceResult!.isApproximate
              ? Colors.orange[300]!
              : AppColors.pickupIconColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _distanceResult!.distanceText,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                if (_distanceResult!.isApproximate) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Approximate distance',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange[700],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Icon(
            _distanceResult!.isApproximate ? Icons.warning_amber : Icons.check_circle,
            color: _distanceResult!.isApproximate ? Colors.orange[700] : AppColors.pickupIconColor,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildAddressInfo() {
    return Column(
      children: [
        if (widget.pickupAddress != null)
          _buildAddressRow(
            icon: Icons.radio_button_checked,
            iconColor: AppColors.pickupIconColor,
            label: 'From',
            address: widget.pickupAddress!,
          ),
        if (widget.pickupAddress != null && widget.dropAddress != null)
          const SizedBox(height: 8),
        if (widget.dropAddress != null)
          _buildAddressRow(
            icon: Icons.location_on,
            iconColor: AppColors.deliveryIconColor,
            label: 'To',
            address: widget.dropAddress!,
          ),
      ],
    );
  }

  Widget _buildAddressRow({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String address,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: iconColor, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                address,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.black87,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
