import 'package:flutter/material.dart';
import 'package:stacked_services/stacked_services.dart';
import '../common/app_colors.dart';

class OrderSuccessDialog extends StatefulWidget {
  final DialogRequest request;
  final Function(DialogResponse) completer;

  const OrderSuccessDialog({
    super.key,
    required this.request,
    required this.completer,
  });

  @override
  State<OrderSuccessDialog> createState() => _OrderSuccessDialogState();
}

class _OrderSuccessDialogState extends State<OrderSuccessDialog>
    with TickerProviderStateMixin {
  late AnimationController _checkAnimationController;
  late AnimationController _blastAnimationController;
  late AnimationController _scaleAnimationController;

  late Animation<double> _checkAnimation;
  late Animation<double> _blastAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Check mark animation controller
    _checkAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Blast animation controller
    _blastAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Scale animation controller
    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Check mark animation
    _checkAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _checkAnimationController,
      curve: Curves.elasticOut,
    ));

    // Blast animation
    _blastAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _blastAnimationController,
      curve: Curves.easeOutQuart,
    ));

    // Scale animation
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleAnimationController,
      curve: Curves.elasticOut,
    ));

    // Start animations in sequence
    _startAnimations();
  }

  void _startAnimations() async {
    // Start scale animation first
    _scaleAnimationController.forward();

    // Wait a bit then start check animation
    await Future.delayed(const Duration(milliseconds: 200));
    _checkAnimationController.forward();

    // Start blast animation slightly after check
    await Future.delayed(const Duration(milliseconds: 300));
    _blastAnimationController.forward();
  }

  @override
  void dispose() {
    _checkAnimationController.dispose();
    _blastAnimationController.dispose();
    _scaleAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orderId = widget.request.data?['orderId'] ?? 'WG${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
    final price = widget.request.data?['price'] ?? '₹80';

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Success Icon with Blast Animation
                  SizedBox(
                    width: 120,
                    height: 120,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Blast Animation Background
                        AnimatedBuilder(
                          animation: _blastAnimation,
                          builder: (context, child) {
                            return CustomPaint(
                              size: const Size(120, 120),
                              painter: BlastPainter(_blastAnimation.value),
                            );
                          },
                        ),

                        // Success Circle with Check Mark
                        AnimatedBuilder(
                          animation: _checkAnimation,
                          builder: (context, child) {
                            return Container(
                              width: 80,
                              height: 80,
                              decoration: const BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                              child: CustomPaint(
                                painter: CheckMarkPainter(_checkAnimation.value),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Success Title
                  const Text(
                    'Order Successful!',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Order Details Container
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.lightBlueBackground,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.borderColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        // Order ID Row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Order ID:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                            Text(
                              orderId,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Price Row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Total Amount:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                            Text(
                              price,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Success Message
                  const Text(
                    'Your parcel booking has been confirmed!\nYou will receive updates via SMS.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      height: 1.5,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // OK Button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () {
                        widget.completer(DialogResponse(confirmed: true));
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        'OK',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

// Custom painter for check mark animation
class CheckMarkPainter extends CustomPainter {
  final double progress;

  CheckMarkPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final checkPath = Path();

    // Define check mark path
    final startPoint = Offset(center.dx - 12, center.dy);
    final middlePoint = Offset(center.dx - 4, center.dy + 8);
    final endPoint = Offset(center.dx + 12, center.dy - 8);

    if (progress > 0) {
      checkPath.moveTo(startPoint.dx, startPoint.dy);

      if (progress <= 0.5) {
        // First half: draw line to middle point
        final currentPoint = Offset.lerp(startPoint, middlePoint, progress * 2)!;
        checkPath.lineTo(currentPoint.dx, currentPoint.dy);
      } else {
        // Second half: draw line to end point
        checkPath.lineTo(middlePoint.dx, middlePoint.dy);
        final currentPoint = Offset.lerp(middlePoint, endPoint, (progress - 0.5) * 2)!;
        checkPath.lineTo(currentPoint.dx, currentPoint.dy);
      }

      canvas.drawPath(checkPath, paint);
    }
  }

  @override
  bool shouldRepaint(CheckMarkPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

// Custom painter for blast animation
class BlastPainter extends CustomPainter {
  final double progress;

  BlastPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    if (progress == 0) return;

    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width / 2;

    // Create multiple circles with different colors and sizes
    final colors = [
      Colors.green.withValues(alpha: 0.3),
      Colors.lightGreen.withValues(alpha: 0.2),
      Colors.greenAccent.withValues(alpha: 0.1),
    ];

    for (int i = 0; i < colors.length; i++) {
      final paint = Paint()
        ..color = colors[i]
        ..style = PaintingStyle.fill;

      final radius = maxRadius * progress * (1 + i * 0.2);
      final opacity = (1 - progress) * colors[i].a;

      paint.color = colors[i].withValues(alpha: opacity / 255);

      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(BlastPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
