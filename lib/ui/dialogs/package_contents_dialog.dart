import 'package:flutter/material.dart';
import 'package:stacked_services/stacked_services.dart';

class PackageContentsDialog extends StatefulWidget {
  final DialogRequest request;
  final Function(DialogResponse) completer;

  const PackageContentsDialog({
    super.key,
    required this.request,
    required this.completer,
  });

  @override
  State<PackageContentsDialog> createState() => _PackageContentsDialogState();
}

class _PackageContentsDialogState extends State<PackageContentsDialog> {
  List<String> selectedContents = [];

  final List<ContentType> contentTypes = [
    ContentType(
      id: 'electronics',
      name: 'Electronics',
      icon: '📱',
      iconColor: Colors.grey.shade800,
    ),
    ContentType(
      id: 'books_documents',
      name: 'Books & Documents',
      icon: '📚',
      iconColor: Colors.grey.shade800,
    ),
    ContentType(
      id: 'household_items',
      name: 'Household items',
      icon: '🏠',
      iconColor: Colors.grey.shade800,
    ),
    ContentType(
      id: 'sports_equipment',
      name: 'Sports Equipment',
      icon: '🏃',
      iconColor: Colors.grey.shade800,
    ),
    ContentType(
      id: 'consumables',
      name: 'Consumables',
      icon: '🍽️',
      iconColor: Colors.grey.shade800,
    ),
  ];

  @override
  void initState() {
    super.initState();
    // Get current selected values from request data
    final currentContents = widget.request.data?['currentContents'] as String?;
    if (currentContents != null && currentContents.isNotEmpty) {
      selectedContents = currentContents.split(', ');
    }
  }

  void toggleSelection(String contentId) {
    setState(() {
      if (selectedContents.contains(contentId)) {
        selectedContents.remove(contentId);
      } else {
        selectedContents.add(contentId);
      }
    });
  }

  void proceedWithSelection() {
    final selectedNames = selectedContents.map((id) {
      final contentType = contentTypes.firstWhere((type) => type.id == id);
      return contentType.name;
    }).toList();

    widget.completer(DialogResponse(
      confirmed: true,
      data: {
        'selectedIds': selectedContents,
        'selectedNames': selectedNames,
        'displayText': selectedNames.join(', '),
      },
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxHeight: 500),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Text(
                    'Describe your package',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => widget.completer(DialogResponse(confirmed: false)),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),

            // Content Types List
            Flexible(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Content type options
                      ...contentTypes.map((contentType) => _buildContentTypeOption(contentType)),
                      const SizedBox(height: 20), // Extra padding at bottom
                    ],
                  ),
                ),
              ),
            ),

            // Proceed Button
            Container(
              padding: const EdgeInsets.all(20),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: selectedContents.isNotEmpty ? proceedWithSelection : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade800,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Proceed',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentTypeOption(ContentType contentType) {
    final isSelected = selectedContents.contains(contentType.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => toggleSelection(contentType.id),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            child: Row(
              children: [
                // Content Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: contentType.iconColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      contentType.icon,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                ),
                const SizedBox(width: 2),

                // Content Name
                Expanded(
                  child: Text(
                    contentType.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ),

                // Check icon (only when selected)
                if (isSelected)
                  const Icon(
                    Icons.check,
                    color: Colors.blue,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ContentType {
  final String id;
  final String name;
  final String icon;
  final Color iconColor;

  ContentType({
    required this.id,
    required this.name,
    required this.icon,
    required this.iconColor,
  });
}
