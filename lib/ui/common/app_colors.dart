import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primaryBlue = Color(0xFF0504aa); // Original blue
  static const Color darkBlue = Color(0xFF1A237E); // Dark blue

  // Button Colors
  static const Color buttonBackground = Color(0xFF424242); // Greyish black
  static const Color buttonText = Color(0xFFFFF59D); // Light yellow

  // Background Colors
  static const Color pageBackground = Color(0xFFF5F5F5); // Light grey
  static const Color lightBlueBackground = Color(0xFFF0F8FF); // Lightest blue
  static const Color whiteBackground = Color(0xFFFFFFFF); // White

  // Border Colors
  static const Color borderColor = Color(0xFF424242); // Grey black
  static const Color lightBorderColor = Color(0xFFE0E0E0); // Light grey border

  // Text Colors
  static const Color primaryText = Color(0xFF212121); // Dark grey
  static const Color secondaryText = Color(0xFF757575); // Medium grey
  static const Color lightText = Color(0xFF9E9E9E); // Light grey
  static const Color whiteText = Color(0xFFFFFFFF); // White

  // Status Colors
  static const Color successColor = Color(0xFF4CAF50); // Green
  static const Color errorColor = Color(0xFFF44336); // Red
  static const Color warningColor = Color(0xFFFFF59D); // Light yellow
  static const Color infoColor = Color(0xFF2196F3); // Blue

  // Icon Colors
  static const Color pickupIconColor = Color(0xFF4CAF50); // Green
  static const Color deliveryIconColor = Color(0xFFF44336); // Red

  // Progress Colors
  static const Color activeStepColor = Color(0xFF1A237E); // Dark blue
  static const Color completedStepColor = Color(0xFF1A237E); // Dark blue
  static const Color inactiveStepColor = Color(0xFFE0E0E0); // Light grey

  // Shadow Colors
  static const Color shadowColor = Color(0x1A000000); // Light black shadow
  static const Color cardShadowColor = Color(0x0D000000); // Very light shadow
}
