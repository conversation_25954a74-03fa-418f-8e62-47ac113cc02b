import 'package:flutter/material.dart';
import '../services/distance_service.dart';
import '../models/address_model.dart';

class DistanceTestHelper {
  static Future<void> testDistanceCalculation() async {
    debugPrint('🧪 Testing Distance Calculation...');
    
    // Test 1: Straight-line distance calculation
    debugPrint('\n1. Testing straight-line distance:');
    final straightDistance = DistanceService.calculateStraightLineDistance(
      lat1: 28.6139, // Delhi
      lon1: 77.2090,
      lat2: 28.4595, // Gurgaon
      lon2: 77.0266,
    );
    debugPrint('   Delhi to Gurgaon: ${straightDistance.toStringAsFixed(2)} km');
    
    // Test 2: Road distance calculation (will fallback to straight-line if no API key)
    debugPrint('\n2. Testing road distance calculation:');
    try {
      final distanceService = DistanceService();
      final roadDistance = await distanceService.calculateRoadDistance(
        originLat: 28.6139,
        originLng: 77.2090,
        destinationLat: 28.4595,
        destinationLng: 77.0266,
      );
      debugPrint('   Road distance result: ${roadDistance.toString()}');
      debugPrint('   Distance: ${roadDistance.distanceText}');
      debugPrint('   Status: ${roadDistance.status}');
      debugPrint('   Is approximate: ${roadDistance.isApproximate}');
    } catch (e) {
      debugPrint('   Error in road distance calculation: $e');
    }
    
    // Test 3: Test with sample addresses
    debugPrint('\n3. Testing with sample addresses:');
    final pickupAddress = AddressModel(
      title: 'Home',
      fullAddress: 'Connaught Place, New Delhi',
      latitude: 28.6315,
      longitude: 77.2167,
    );
    
    final dropAddress = AddressModel(
      title: 'Office',
      fullAddress: 'Cyber City, Gurgaon',
      latitude: 28.4949,
      longitude: 77.0787,
    );
    
    final testDistance = DistanceService.calculateStraightLineDistance(
      lat1: pickupAddress.latitude,
      lon1: pickupAddress.longitude,
      lat2: dropAddress.latitude,
      lon2: dropAddress.longitude,
    );
    
    debugPrint('   From: ${pickupAddress.fullAddress}');
    debugPrint('   To: ${dropAddress.fullAddress}');
    debugPrint('   Distance: ${testDistance.toStringAsFixed(2)} km');
    
    debugPrint('\n✅ Distance calculation test completed!');
  }
  
  static void testAddressCoordinates(Map<String, dynamic>? addressDetails) {
    debugPrint('🔍 Testing address coordinates:');
    if (addressDetails == null) {
      debugPrint('   ❌ Address details are null');
      return;
    }
    
    debugPrint('   Address: ${addressDetails['fullAddress']}');
    debugPrint('   Latitude: ${addressDetails['latitude']}');
    debugPrint('   Longitude: ${addressDetails['longitude']}');
    debugPrint('   Latitude type: ${addressDetails['latitude'].runtimeType}');
    debugPrint('   Longitude type: ${addressDetails['longitude'].runtimeType}');
    
    // Test conversion to double
    try {
      final lat = addressDetails['latitude']?.toDouble();
      final lng = addressDetails['longitude']?.toDouble();
      debugPrint('   Converted Latitude: $lat');
      debugPrint('   Converted Longitude: $lng');
      
      if (lat != null && lng != null) {
        debugPrint('   ✅ Coordinates are valid');
      } else {
        debugPrint('   ❌ Coordinates conversion failed');
      }
    } catch (e) {
      debugPrint('   ❌ Error converting coordinates: $e');
    }
  }
  
  static void debugDistanceWidget({
    double? pickupLat,
    double? pickupLng,
    double? dropLat,
    double? dropLng,
    String? pickupAddress,
    String? dropAddress,
  }) {
    debugPrint('🎯 Distance Widget Debug Info:');
    debugPrint('   Pickup Lat: $pickupLat (${pickupLat.runtimeType})');
    debugPrint('   Pickup Lng: $pickupLng (${pickupLng.runtimeType})');
    debugPrint('   Drop Lat: $dropLat (${dropLat.runtimeType})');
    debugPrint('   Drop Lng: $dropLng (${dropLng.runtimeType})');
    debugPrint('   Pickup Address: $pickupAddress');
    debugPrint('   Drop Address: $dropAddress');
    
    final canCalculate = pickupLat != null && 
                        pickupLng != null && 
                        dropLat != null && 
                        dropLng != null;
    
    debugPrint('   Can calculate distance: $canCalculate');
    
    if (canCalculate) {
      final distance = DistanceService.calculateStraightLineDistance(
        lat1: pickupLat!,
        lon1: pickupLng!,
        lat2: dropLat!,
        lon2: dropLng!,
      );
      debugPrint('   Calculated distance: ${distance.toStringAsFixed(2)} km');
    }
  }
}
