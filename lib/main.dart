import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app/app.locator.dart';
import 'app/app.router.dart';
import 'firebase_options.dart';
import 'ui/common/app_colors.dart';
import 'ui/common/app_text_styles.dart';
import 'services/logging_service.dart';
import 'app/app.dialogs.dart';
import 'debug/distance_test.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logging first
  final logger = LoggingService();
  logger.initialize();

  logger.logApp('Starting app initialization...');

  try {
    // Initialize Firebase
    logger.logFirebase('Initializing Firebase...');
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    logger.logFirebase('Firebase initialized successfully');

    // Setup locator for dependency injection
    await setupLocator();

    // Setup dialog UI
    setupDialogUi();

    // Test distance calculation on startup (for debugging)
    DistanceTestHelper.testDistanceCalculation();

    logger.logApp('Starting app...');
    runApp(const MyApp());
  } catch (e) {
    logger.error('Error during initialization', e);
    // Run app anyway with a simple error screen
    runApp(MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Initialization Error'),
              const SizedBox(height: 8),
              Text('$e', textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    ));
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Winget Express',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: AppColors.darkBlue),
        useMaterial3: true,
        textTheme: GoogleFonts.poppinsTextTheme(
          ThemeData.light().textTheme.copyWith(
            displayLarge: AppTextStyles.heading1,
            displayMedium: AppTextStyles.heading2,
            displaySmall: AppTextStyles.heading3,
            headlineLarge: AppTextStyles.heading4,
            headlineMedium: AppTextStyles.heading5,
            headlineSmall: AppTextStyles.heading6,
            titleLarge: AppTextStyles.cardTitle,
            titleMedium: AppTextStyles.labelLarge,
            titleSmall: AppTextStyles.labelMedium,
            bodyLarge: AppTextStyles.bodyLarge,
            bodyMedium: AppTextStyles.bodyMedium,
            bodySmall: AppTextStyles.bodySmall,
            labelLarge: AppTextStyles.buttonLarge,
            labelMedium: AppTextStyles.buttonMedium,
            labelSmall: AppTextStyles.buttonSmall,
          ),
        ),
        scaffoldBackgroundColor: AppColors.pageBackground,
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.whiteBackground,
          foregroundColor: AppColors.primaryText,
          elevation: 0,
          titleTextStyle: AppTextStyles.appBarTitle,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.buttonBackground,
            foregroundColor: AppColors.buttonText,
            textStyle: AppTextStyles.buttonLarge,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          labelStyle: AppTextStyles.labelMedium,
          hintStyle: AppTextStyles.bodyMediumWith(color: AppColors.secondaryText),
          errorStyle: AppTextStyles.errorText,
        ),
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      navigatorKey: StackedService.navigatorKey,
      onGenerateRoute: StackedRouter().onGenerateRoute,
    );
  }
}
