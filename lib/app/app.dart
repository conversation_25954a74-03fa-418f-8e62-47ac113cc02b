import 'package:stacked/stacked_annotations.dart';
import 'package:stacked_services/stacked_services.dart';

import '../services/auth_service.dart';
import '../services/logging_service.dart';
import '../services/address_service.dart';
import '../ui/views/signin/signin_view.dart';
import '../ui/views/otp_verification/otp_verification_view.dart';
import '../ui/views/home/<USER>';
import '../ui/views/startup/startup_view.dart';
import '../ui/views/main_navigation/main_navigation_view.dart';
import '../ui/views/order/order_view.dart';
import '../ui/views/explore/explore_view.dart';
import '../ui/views/accounts/accounts_view.dart';
import '../ui/views/local_parcel/local_parcel_view.dart';
import '../ui/views/all_india_parcel/all_india_parcel_view.dart';
import '../ui/views/vehicle_booking/vehicle_booking_view.dart';
import '../ui/views/map_picker/map_picker_view.dart';
import '../ui/dialogs/address_selection_dialog.dart';
import '../ui/dialogs/package_description_dialog.dart';
import '../ui/dialogs/package_contents_dialog.dart';
import '../ui/dialogs/order_success_dialog.dart';

@StackedApp(
  routes: [
    MaterialRoute(page: StartupView, initial: true),
    MaterialRoute(page: SignInView),
    MaterialRoute(page: OtpVerificationView),
    MaterialRoute(page: MainNavigationView),
    MaterialRoute(page: HomeView),
    MaterialRoute(page: OrderView),
    MaterialRoute(page: ExploreView),
    MaterialRoute(page: AccountsView),
    MaterialRoute(page: LocalParcelView),
    MaterialRoute(page: AllIndiaParcelView),
    MaterialRoute(page: VehicleBookingView),
    MaterialRoute(page: MapPickerView),
  ],
  dialogs: [
    StackedDialog(classType: AddressSelectionDialog),
    StackedDialog(classType: PackageDescriptionDialog),
    StackedDialog(classType: PackageContentsDialog),
    StackedDialog(classType: OrderSuccessDialog),
  ],
  dependencies: [
    LazySingleton(classType: NavigationService),
    LazySingleton(classType: DialogService),
    LazySingleton(classType: SnackbarService),
    LazySingleton(classType: AuthService),
    LazySingleton(classType: LoggingService),
    LazySingleton(classType: AddressService),
  ],
)
class App {}
