// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// StackedNavigatorGenerator
// **************************************************************************

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flutter/foundation.dart' as _i16;
import 'package:flutter/material.dart' as _i15;
import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart' as _i1;
import 'package:stacked_services/stacked_services.dart' as _i18;
import 'package:winget_frontend/models/address_model.dart' as _i17;
import 'package:winget_frontend/ui/views/accounts/accounts_view.dart' as _i9;
import 'package:winget_frontend/ui/views/all_india_parcel/all_india_parcel_view.dart'
    as _i11;
import 'package:winget_frontend/ui/views/cab_booking/cab_booking_view.dart'
    as _i13;
import 'package:winget_frontend/ui/views/explore/explore_view.dart' as _i8;
import 'package:winget_frontend/ui/views/home/<USER>' as _i6;
import 'package:winget_frontend/ui/views/local_parcel/local_parcel_view.dart'
    as _i10;
import 'package:winget_frontend/ui/views/main_navigation/main_navigation_view.dart'
    as _i5;
import 'package:winget_frontend/ui/views/map_picker/map_picker_view.dart'
    as _i14;
import 'package:winget_frontend/ui/views/order/order_view.dart' as _i7;
import 'package:winget_frontend/ui/views/otp_verification/otp_verification_view.dart'
    as _i4;
import 'package:winget_frontend/ui/views/signin/signin_view.dart' as _i3;
import 'package:winget_frontend/ui/views/startup/startup_view.dart' as _i2;
import 'package:winget_frontend/ui/views/vehicle_booking/vehicle_booking_view.dart'
    as _i12;

class Routes {
  static const startupView = '/';

  static const signInView = '/sign-in-view';

  static const otpVerificationView = '/otp-verification-view';

  static const mainNavigationView = '/main-navigation-view';

  static const homeView = '/home-view';

  static const orderView = '/order-view';

  static const exploreView = '/explore-view';

  static const accountsView = '/accounts-view';

  static const localParcelView = '/local-parcel-view';

  static const allIndiaParcelView = '/all-india-parcel-view';

  static const vehicleBookingView = '/vehicle-booking-view';

  static const cabBookingView = '/cab-booking-view';

  static const mapPickerView = '/map-picker-view';

  static const all = <String>{
    startupView,
    signInView,
    otpVerificationView,
    mainNavigationView,
    homeView,
    orderView,
    exploreView,
    accountsView,
    localParcelView,
    allIndiaParcelView,
    vehicleBookingView,
    cabBookingView,
    mapPickerView,
  };
}

class StackedRouter extends _i1.RouterBase {
  final _routes = <_i1.RouteDef>[
    _i1.RouteDef(
      Routes.startupView,
      page: _i2.StartupView,
    ),
    _i1.RouteDef(
      Routes.signInView,
      page: _i3.SignInView,
    ),
    _i1.RouteDef(
      Routes.otpVerificationView,
      page: _i4.OtpVerificationView,
    ),
    _i1.RouteDef(
      Routes.mainNavigationView,
      page: _i5.MainNavigationView,
    ),
    _i1.RouteDef(
      Routes.homeView,
      page: _i6.HomeView,
    ),
    _i1.RouteDef(
      Routes.orderView,
      page: _i7.OrderView,
    ),
    _i1.RouteDef(
      Routes.exploreView,
      page: _i8.ExploreView,
    ),
    _i1.RouteDef(
      Routes.accountsView,
      page: _i9.AccountsView,
    ),
    _i1.RouteDef(
      Routes.localParcelView,
      page: _i10.LocalParcelView,
    ),
    _i1.RouteDef(
      Routes.allIndiaParcelView,
      page: _i11.AllIndiaParcelView,
    ),
    _i1.RouteDef(
      Routes.vehicleBookingView,
      page: _i12.VehicleBookingView,
    ),
    _i1.RouteDef(
      Routes.cabBookingView,
      page: _i13.CabBookingView,
    ),
    _i1.RouteDef(
      Routes.mapPickerView,
      page: _i14.MapPickerView,
    ),
  ];

  final _pagesMap = <Type, _i1.StackedRouteFactory>{
    _i2.StartupView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i2.StartupView(),
        settings: data,
      );
    },
    _i3.SignInView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i3.SignInView(),
        settings: data,
      );
    },
    _i4.OtpVerificationView: (data) {
      final args = data.getArgs<OtpVerificationViewArguments>(nullOk: false);
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => _i4.OtpVerificationView(
            key: args.key, phoneNumber: args.phoneNumber),
        settings: data,
      );
    },
    _i5.MainNavigationView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i5.MainNavigationView(),
        settings: data,
      );
    },
    _i6.HomeView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i6.HomeView(),
        settings: data,
      );
    },
    _i7.OrderView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i7.OrderView(),
        settings: data,
      );
    },
    _i8.ExploreView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i8.ExploreView(),
        settings: data,
      );
    },
    _i9.AccountsView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i9.AccountsView(),
        settings: data,
      );
    },
    _i10.LocalParcelView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i10.LocalParcelView(),
        settings: data,
      );
    },
    _i11.AllIndiaParcelView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i11.AllIndiaParcelView(),
        settings: data,
      );
    },
    _i12.VehicleBookingView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i12.VehicleBookingView(),
        settings: data,
      );
    },
    _i13.CabBookingView: (data) {
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) => const _i13.CabBookingView(),
        settings: data,
      );
    },
    _i14.MapPickerView: (data) {
      final args = data.getArgs<MapPickerViewArguments>(
        orElse: () => const MapPickerViewArguments(),
      );
      return _i15.MaterialPageRoute<dynamic>(
        builder: (context) =>
            _i14.MapPickerView(key: args.key, editAddress: args.editAddress),
        settings: data,
      );
    },
  };

  @override
  List<_i1.RouteDef> get routes => _routes;

  @override
  Map<Type, _i1.StackedRouteFactory> get pagesMap => _pagesMap;
}

class OtpVerificationViewArguments {
  const OtpVerificationViewArguments({
    this.key,
    required this.phoneNumber,
  });

  final _i16.Key? key;

  final String phoneNumber;

  @override
  String toString() {
    return '{"key": "$key", "phoneNumber": "$phoneNumber"}';
  }

  @override
  bool operator ==(covariant OtpVerificationViewArguments other) {
    if (identical(this, other)) return true;
    return other.key == key && other.phoneNumber == phoneNumber;
  }

  @override
  int get hashCode {
    return key.hashCode ^ phoneNumber.hashCode;
  }
}

class MapPickerViewArguments {
  const MapPickerViewArguments({
    this.key,
    this.editAddress,
  });

  final _i16.Key? key;

  final _i17.AddressModel? editAddress;

  @override
  String toString() {
    return '{"key": "$key", "editAddress": "$editAddress"}';
  }

  @override
  bool operator ==(covariant MapPickerViewArguments other) {
    if (identical(this, other)) return true;
    return other.key == key && other.editAddress == editAddress;
  }

  @override
  int get hashCode {
    return key.hashCode ^ editAddress.hashCode;
  }
}

extension NavigatorStateExtension on _i18.NavigationService {
  Future<dynamic> navigateToStartupView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.startupView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToSignInView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.signInView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToOtpVerificationView({
    _i16.Key? key,
    required String phoneNumber,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  }) async {
    return navigateTo<dynamic>(Routes.otpVerificationView,
        arguments:
            OtpVerificationViewArguments(key: key, phoneNumber: phoneNumber),
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToMainNavigationView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.mainNavigationView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToHomeView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.homeView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToOrderView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.orderView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToExploreView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.exploreView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToAccountsView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.accountsView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToLocalParcelView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.localParcelView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToAllIndiaParcelView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.allIndiaParcelView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToVehicleBookingView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.vehicleBookingView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToCabBookingView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return navigateTo<dynamic>(Routes.cabBookingView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> navigateToMapPickerView({
    _i16.Key? key,
    _i17.AddressModel? editAddress,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  }) async {
    return navigateTo<dynamic>(Routes.mapPickerView,
        arguments: MapPickerViewArguments(key: key, editAddress: editAddress),
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithStartupView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.startupView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithSignInView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.signInView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithOtpVerificationView({
    _i16.Key? key,
    required String phoneNumber,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  }) async {
    return replaceWith<dynamic>(Routes.otpVerificationView,
        arguments:
            OtpVerificationViewArguments(key: key, phoneNumber: phoneNumber),
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithMainNavigationView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.mainNavigationView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithHomeView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.homeView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithOrderView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.orderView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithExploreView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.exploreView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithAccountsView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.accountsView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithLocalParcelView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.localParcelView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithAllIndiaParcelView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.allIndiaParcelView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithVehicleBookingView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.vehicleBookingView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithCabBookingView([
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  ]) async {
    return replaceWith<dynamic>(Routes.cabBookingView,
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }

  Future<dynamic> replaceWithMapPickerView({
    _i16.Key? key,
    _i17.AddressModel? editAddress,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
        transition,
  }) async {
    return replaceWith<dynamic>(Routes.mapPickerView,
        arguments: MapPickerViewArguments(key: key, editAddress: editAddress),
        id: routerId,
        preventDuplicates: preventDuplicates,
        parameters: parameters,
        transition: transition);
  }
}
