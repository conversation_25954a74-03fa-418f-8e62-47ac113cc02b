// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// StackedDialogGenerator
// **************************************************************************

import 'package:stacked_services/stacked_services.dart';

import 'app.locator.dart';
import '../ui/dialogs/address_selection_dialog.dart';
import '../ui/dialogs/order_success_dialog.dart';
import '../ui/dialogs/package_contents_dialog.dart';
import '../ui/dialogs/package_description_dialog.dart';

enum DialogType {
  addressSelection,
  packageDescription,
  packageContents,
  orderSuccess,
}

void setupDialogUi() {
  final dialogService = locator<DialogService>();

  final Map<DialogType, DialogBuilder> builders = {
    DialogType.addressSelection: (context, request, completer) =>
        AddressSelectionDialog(request: request, completer: completer),
    DialogType.packageDescription: (context, request, completer) =>
        PackageDescriptionDialog(request: request, completer: completer),
    DialogType.packageContents: (context, request, completer) =>
        PackageContentsDialog(request: request, completer: completer),
    DialogType.orderSuccess: (context, request, completer) =>
        OrderSuccessDialog(request: request, completer: completer),
  };

  dialogService.registerCustomDialogBuilders(builders);
}
