# Backend API Endpoints for Flutter OTP Authentication

This document describes the backend API endpoints required for the Flutter OTP authentication app.

## Required Endpoints

### 1. Verify Firebase ID Token

**Endpoint:** `POST /auth/verify-token`

**Description:** Verifies the Firebase ID token and returns access/refresh tokens.

**Request Body:**
```json
{
  "idToken": "firebase_id_token_here"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Authentication successful",
  "tokens": {
    "accessToken": "jwt_access_token_here",
    "refreshToken": "jwt_refresh_token_here"
  },
  "user": {
    "id": "user_id",
    "phoneNumber": "+************",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "message": "Invalid token"
}
```

### 2. Refresh Access Token

**Endpoint:** `POST /auth/refresh-token`

**Description:** Refreshes the access token using the refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "tokens": {
    "accessToken": "new_jwt_access_token_here",
    "refreshToken": "new_jwt_refresh_token_here"
  }
}
```

### 3. Get User Profile

**Endpoint:** `GET /auth/me`

**Headers:**
```
Authorization: Bearer access_token_here
Content-Type: application/json
```

**Response (Success):**
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "phoneNumber": "+************",
    "name": "User Name",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 4. Update User Profile

**Endpoint:** `PUT /auth/profile`

**Headers:**
```
Authorization: Bearer access_token_here
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Updated Name",
  "email": "<EMAIL>"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "user": {
    "id": "user_id",
    "phoneNumber": "+************",
    "name": "Updated Name",
    "email": "<EMAIL>",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

## Implementation Notes

### Firebase Admin SDK Setup

Your backend should use Firebase Admin SDK to verify the ID tokens:

```javascript
// Node.js example
const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.applicationDefault(),
  // or use service account key
});

// Verify ID token
async function verifyIdToken(idToken) {
  try {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    throw new Error('Invalid token');
  }
}
```

### JWT Token Generation

Generate JWT tokens for access and refresh:

```javascript
const jwt = require('jsonwebtoken');

function generateTokens(userId) {
  const accessToken = jwt.sign(
    { userId, type: 'access' },
    process.env.JWT_SECRET,
    { expiresIn: '15m' }
  );
  
  const refreshToken = jwt.sign(
    { userId, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: '7d' }
  );
  
  return { accessToken, refreshToken };
}
```

### Database Schema

Suggested user table structure:

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number VARCHAR(20) UNIQUE NOT NULL,
  name VARCHAR(255),
  email VARCHAR(255),
  firebase_uid VARCHAR(255) UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE refresh_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  token_hash VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Security Considerations

1. **Token Validation:** Always validate Firebase ID tokens on the server
2. **Rate Limiting:** Implement rate limiting for authentication endpoints
3. **HTTPS Only:** Use HTTPS in production
4. **Token Expiry:** Set appropriate expiry times for tokens
5. **Refresh Token Rotation:** Rotate refresh tokens on each use
6. **Input Validation:** Validate all input data
7. **Error Handling:** Don't expose sensitive information in error messages

### Environment Variables

```env
FIREBASE_PROJECT_ID=your-project-id
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-jwt-refresh-secret-key
DATABASE_URL=your-database-url
```

## Testing

Use tools like Postman or curl to test your endpoints:

```bash
# Test verify token
curl -X POST http://localhost:3000/auth/verify-token \
  -H "Content-Type: application/json" \
  -d '{"idToken": "firebase_id_token"}'

# Test get profile
curl -X GET http://localhost:3000/auth/me \
  -H "Authorization: Bearer access_token" \
  -H "Content-Type: application/json"
```
