# Winget Express - Flutter OTP Authentication

A Flutter application with Firebase OTP authentication and backend token verification.

## Features

- 📱 Phone number authentication using Firebase
- 🔐 OTP verification with 6-digit PIN input
- 🔄 Token refresh mechanism
- 🏠 Home screen with user profile
- 🎨 Modern Material Design 3 UI
- 🔒 Secure token storage using SharedPreferences

## Setup Instructions

### 1. Firebase Setup

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication and Phone Sign-in method
3. Add your app to the Firebase project:
   - **Android**: Add Android app with package name `com.winget.express.winget_frontend`
   - **iOS**: Add iOS app with bundle ID `com.winget.express.wingetFrontend`
4. Download configuration files:
   - `google-services.json` for Android (place in `android/app/`)
   - `GoogleService-Info.plist` for iOS (place in `ios/Runner/`)

### 2. Update Firebase Configuration

Update the `lib/firebase_options.dart` file with your actual Firebase configuration values:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'your-actual-android-api-key',
  appId: 'your-actual-android-app-id',
  messagingSenderId: 'your-actual-sender-id',
  projectId: 'your-actual-project-id',
  storageBucket: 'your-actual-project-id.appspot.com',
);
```

### 3. Backend Configuration

Update the `baseUrl` in `lib/services/auth_service.dart`:

```dart
final String baseUrl = 'https://your-actual-backend-url.com/api';
```

Your backend should implement these endpoints:
- `POST /auth/verify-token` - Verify Firebase ID token
- `POST /auth/refresh-token` - Refresh access token
- `GET /auth/me` - Get user profile
- `PUT /auth/profile` - Update user profile

### 4. Install Dependencies

```bash
flutter pub get
```

### 5. Run the App

```bash
flutter run
```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── firebase_options.dart     # Firebase configuration
├── services/
│   └── auth_service.dart     # Authentication service
└── screens/
    ├── signin_screen.dart    # Phone number input screen
    ├── otp_verification_screen.dart  # OTP verification screen
    └── home_screen.dart      # Home screen after authentication
```

## Authentication Flow

1. **Sign In**: User enters phone number
2. **OTP Sent**: Firebase sends OTP to the phone
3. **OTP Verification**: User enters 6-digit OTP
4. **Token Exchange**: Firebase ID token is sent to backend
5. **Backend Verification**: Backend verifies token and returns access/refresh tokens
6. **Home Screen**: User is redirected to home screen

## Dependencies

- `firebase_core`: Firebase SDK core
- `firebase_auth`: Firebase Authentication
- `http`: HTTP client for backend communication
- `provider`: State management
- `shared_preferences`: Local storage
- `pinput`: Beautiful PIN input widget

## Troubleshooting

### Common Issues

1. **Firebase not initialized**: Ensure `google-services.json` and `GoogleService-Info.plist` are properly added
2. **OTP not received**: Check Firebase console for phone authentication setup
3. **Backend errors**: Verify your backend URL and endpoint implementations
4. **Token issues**: Check token storage and refresh logic

For development, you can add test phone numbers in Firebase Console under Authentication > Sign-in method > Phone.
