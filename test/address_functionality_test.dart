import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:winget_frontend/models/address_model.dart';
import 'package:winget_frontend/services/address_service.dart';
import 'package:winget_frontend/services/distance_service.dart';

void main() {
  group('Address Functionality Tests', () {
    late AddressService addressService;

    setUp(() {
      addressService = AddressService();
    });

    test('AddressModel should create correctly', () {
      final address = AddressModel(
        title: 'Home',
        fullAddress: '123 Main Street, City, State',
        latitude: 28.6139,
        longitude: 77.2090,
        addressType: 'home',
        isDefault: true,
      );

      expect(address.title, 'Home');
      expect(address.fullAddress, '123 Main Street, City, State');
      expect(address.latitude, 28.6139);
      expect(address.longitude, 77.2090);
      expect(address.addressType, 'home');
      expect(address.isDefault, true);
      expect(address.typeIcon, '🏠');
    });

    test('AddressModel should convert to/from JSON correctly', () {
      final originalAddress = AddressModel(
        id: '123',
        title: 'Office',
        fullAddress: '456 Business Ave, City, State',
        landmark: 'Near Metro Station',
        latitude: 28.7041,
        longitude: 77.1025,
        addressType: 'work',
        contactName: '<PERSON> Doe',
        contactPhone: '+1234567890',
        isDefault: false,
      );

      final json = originalAddress.toJson();
      final recreatedAddress = AddressModel.fromJson(json);

      expect(recreatedAddress.id, originalAddress.id);
      expect(recreatedAddress.title, originalAddress.title);
      expect(recreatedAddress.fullAddress, originalAddress.fullAddress);
      expect(recreatedAddress.landmark, originalAddress.landmark);
      expect(recreatedAddress.latitude, originalAddress.latitude);
      expect(recreatedAddress.longitude, originalAddress.longitude);
      expect(recreatedAddress.addressType, originalAddress.addressType);
      expect(recreatedAddress.contactName, originalAddress.contactName);
      expect(recreatedAddress.contactPhone, originalAddress.contactPhone);
      expect(recreatedAddress.isDefault, originalAddress.isDefault);
    });

    test('AddressModel should handle different address types', () {
      final homeAddress = AddressModel(
        title: 'Home',
        fullAddress: 'Home Address',
        latitude: 0.0,
        longitude: 0.0,
        addressType: 'home',
      );

      final workAddress = AddressModel(
        title: 'Work',
        fullAddress: 'Work Address',
        latitude: 0.0,
        longitude: 0.0,
        addressType: 'work',
      );

      final otherAddress = AddressModel(
        title: 'Other',
        fullAddress: 'Other Address',
        latitude: 0.0,
        longitude: 0.0,
        addressType: 'other',
      );

      expect(homeAddress.typeIcon, '🏠');
      expect(workAddress.typeIcon, '🏢');
      expect(otherAddress.typeIcon, '📍');
    });

    test('AddressModel should handle display address with landmark', () {
      final addressWithLandmark = AddressModel(
        title: 'Home',
        fullAddress: '123 Main Street',
        landmark: 'Near Park',
        latitude: 0.0,
        longitude: 0.0,
      );

      final addressWithoutLandmark = AddressModel(
        title: 'Home',
        fullAddress: '123 Main Street',
        latitude: 0.0,
        longitude: 0.0,
      );

      expect(addressWithLandmark.displayAddress, '123 Main Street\nNear Park');
      expect(addressWithoutLandmark.displayAddress, '123 Main Street');
    });

    test('AddressModel copyWith should work correctly', () {
      final originalAddress = AddressModel(
        title: 'Home',
        fullAddress: '123 Main Street',
        latitude: 28.6139,
        longitude: 77.2090,
        isDefault: false,
      );

      final updatedAddress = originalAddress.copyWith(
        title: 'Updated Home',
        isDefault: true,
      );

      expect(updatedAddress.title, 'Updated Home');
      expect(updatedAddress.fullAddress, originalAddress.fullAddress);
      expect(updatedAddress.latitude, originalAddress.latitude);
      expect(updatedAddress.longitude, originalAddress.longitude);
      expect(updatedAddress.isDefault, true);
    });

    test('AddressService should initialize without errors', () {
      expect(addressService, isNotNull);
    });

    test('Distance calculation should work with straight-line distance', () {
      // Test straight-line distance calculation
      final distance = DistanceService.calculateStraightLineDistance(
        lat1: 28.6139, // Delhi
        lon1: 77.2090,
        lat2: 28.4595, // Gurgaon
        lon2: 77.0266,
      );

      expect(distance, greaterThan(0));
      expect(distance, lessThan(100)); // Should be reasonable distance
      debugPrint('Distance between Delhi and Gurgaon: ${distance.toStringAsFixed(2)} km');
    });
  });
}
