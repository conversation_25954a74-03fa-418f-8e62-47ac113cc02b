import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:winget_frontend/ui/views/local_parcel/local_parcel_view.dart';

void main() {
  group('Local Parcel View Tests', () {
    testWidgets('Local Parcel view should display correctly', (WidgetTester tester) async {
      // Build the LocalParcelView widget
      await tester.pumpWidget(
        const MaterialApp(
          home: LocalParcelView(),
        ),
      );

      // Verify that the app bar title is displayed
      expect(find.text('Local Parcel'), findsOneWidget);

      // Verify that the progress indicators are displayed
      expect(find.text('Address'), findsOneWidget);
      expect(find.text('Package'), findsOneWidget);
      expect(find.text('Estimate'), findsOneWidget);
      expect(find.text('Review'), findsOneWidget);

      // Verify that the pickup and delivery sections are displayed
      expect(find.text('PICKUP AT'), findsOneWidget);
      expect(find.text('DELIVERY TO'), findsOneWidget);

      // Verify that the buttons are displayed
      expect(find.text('Add pickup details'), findsOneWidget);
      expect(find.text('Add drop details'), findsOneWidget);
      expect(find.text('Next'), findsOneWidget);

      // Verify that the back button is present
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('Progress indicator should show correct active step', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LocalParcelView(),
        ),
      );

      // The first step (Address) should be active by default
      // We can verify this by checking if the Address step has the active styling
      expect(find.text('Address'), findsOneWidget);
      
      // Verify that the location icons are present
      expect(find.byIcon(Icons.keyboard_arrow_up), findsOneWidget);
      expect(find.byIcon(Icons.keyboard_arrow_down), findsOneWidget);
    });

    testWidgets('Buttons should be tappable', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LocalParcelView(),
        ),
      );

      // Test tapping the pickup details button
      await tester.tap(find.text('Add pickup details'));
      await tester.pump();

      // Test tapping the drop details button
      await tester.tap(find.text('Add drop details'));
      await tester.pump();

      // Test tapping the Next button (should be disabled initially)
      await tester.tap(find.text('Next'));
      await tester.pump();

      // No exceptions should be thrown
    });

    testWidgets('Back button should be functional', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const LocalParcelView(),
                    ),
                  );
                },
                child: const Text('Go to Local Parcel'),
              ),
            ),
          ),
        ),
      );

      // Navigate to Local Parcel view
      await tester.tap(find.text('Go to Local Parcel'));
      await tester.pumpAndSettle();

      // Verify we're on the Local Parcel view
      expect(find.text('Local Parcel'), findsOneWidget);

      // Tap the back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify we're back to the original screen
      expect(find.text('Go to Local Parcel'), findsOneWidget);
    });
  });
}
