// This is a basic Flutter widget test for the authentication app.

import 'package:flutter_test/flutter_test.dart';
import 'package:winget_frontend/main.dart';

void main() {
  testWidgets('App loads startup screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the startup screen loads
    expect(find.text('Winget Express'), findsOneWidget);
    expect(find.text('Initializing...'), findsOneWidget);
  });
}
