# 🔧 Distance Calculation Troubleshooting Guide

## 🚨 **Common Issues & Solutions**

### **Issue 1: Distance Calculator Widget Not Appearing**

**Symptoms:**
- Both pickup and drop addresses are selected
- Distance calculator widget doesn't show up

**Debugging Steps:**
1. Check if both addresses have coordinates:
```dart
// This will be logged automatically when addresses are selected
debugPrint('🎯 Both addresses available, testing distance calculation...');
```

2. Verify address data structure:
```dart
// Check console for these debug messages:
// "🔍 Testing address coordinates:"
// "Address: [address]"
// "Latitude: [lat]" 
// "Longitude: [lng]"
```

**Solutions:**
- Ensure addresses are saved with valid latitude/longitude values
- Check that `hasPickupAddress` and `hasDeliveryAddress` return true
- Verify the condition in local_parcel_view.dart:
```dart
if (viewModel.hasPickupAddress && viewModel.hasDeliveryAddress)
```

### **Issue 2: Distance Calculation Returns "Calculating distance..."**

**Symptoms:**
- Widget appears but shows "Calculating distance..." indefinitely
- No distance result displayed

**Debugging Steps:**
1. Check console for distance calculation logs:
```
🧪 Testing Distance Calculation...
1. Testing straight-line distance:
   Delhi to Gurgaon: [X.XX] km
```

2. Look for API errors:
```
Error calculating road distance: [error message]
```

**Solutions:**
- **If using Google Maps API**: Verify API key is configured correctly
- **If API fails**: System should fallback to straight-line distance automatically
- **Check coordinates**: Ensure latitude/longitude values are valid numbers

### **Issue 3: Google Maps API Key Issues**

**Symptoms:**
- Distance calculation fails
- Console shows API key errors

**Solutions:**
1. **Get Google Maps API Key:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Enable Distance Matrix API
   - Create API key
   - Restrict API key properly

2. **Configure API Key:**
   - Update `lib/services/distance_service.dart`:
   ```dart
   static const String _googleMapsApiKey = 'YOUR_ACTUAL_API_KEY';
   ```

3. **Verify API is enabled:**
   - Distance Matrix API
   - Geocoding API (for address lookup)

### **Issue 4: Coordinates Are Null or Invalid**

**Symptoms:**
- Console shows "❌ Coordinates conversion failed"
- Distance widget shows empty state

**Debugging Steps:**
1. Check address selection logs:
```
Pickup address selected: [title]
🔍 Testing address coordinates:
   Latitude type: [type]
   Longitude type: [type]
```

2. Verify map picker saves coordinates:
```
Address saved successfully
```

**Solutions:**
- Ensure map picker sets valid coordinates when saving addresses
- Check that `updateLocation()` is called with valid lat/lng values
- Verify address model stores coordinates as double values

### **Issue 5: Navigation Issues in Map Picker**

**Symptoms:**
- Map picker doesn't close after saving address
- Address not returned to local parcel view

**Solutions:**
- Updated navigation to use proper context handling
- Address should be returned when save button is pressed
- Check console for "Address saved successfully" message

## 🧪 **Testing Steps**

### **Step 1: Test Basic Distance Calculation**
Run the app and check console for:
```
🧪 Testing Distance Calculation...
1. Testing straight-line distance:
   Delhi to Gurgaon: [X.XX] km
2. Testing road distance calculation:
   [Results or error messages]
```

### **Step 2: Test Address Selection Flow**
1. Go to Local Parcel screen
2. Click "Add pickup details"
3. Select or add an address
4. Check console for:
```
Pickup address selected: [title]
🔍 Testing address coordinates:
   Address: [full address]
   Latitude: [value]
   Longitude: [value]
   ✅ Coordinates are valid
```

### **Step 3: Test Distance Widget**
1. Select both pickup and drop addresses
2. Distance widget should appear automatically
3. Check console for:
```
🎯 Both addresses available, testing distance calculation...
Distance Widget Debug Info:
   Pickup Lat: [value]
   Pickup Lng: [value]
   Drop Lat: [value]
   Drop Lng: [value]
   Can calculate distance: true
   Calculated distance: [X.XX] km
```

## 🔍 **Debug Information**

### **Console Messages to Look For:**

**✅ Success Messages:**
- `🧪 Testing Distance Calculation...`
- `Address saved successfully`
- `✅ Coordinates are valid`
- `Can calculate distance: true`
- `Distance calculated: [X.XX] km`

**❌ Error Messages:**
- `❌ Address details are null`
- `❌ Coordinates conversion failed`
- `Error calculating road distance:`
- `Can calculate distance: false`

### **Expected Flow:**
1. User selects pickup address → Coordinates logged and validated
2. User selects drop address → Coordinates logged and validated
3. Distance widget appears → Calculation starts automatically
4. Distance displayed → Either API result or fallback straight-line distance

## 🛠️ **Manual Testing**

### **Test Straight-Line Distance:**
```dart
final distance = DistanceService.calculateStraightLineDistance(
  lat1: 28.6139, // Delhi
  lon1: 77.2090,
  lat2: 28.4595, // Gurgaon
  lon2: 77.0266,
);
// Should return approximately 25-30 km
```

### **Test Address Coordinates:**
```dart
DistanceTestHelper.testAddressCoordinates(addressDetails);
// Check if coordinates are properly stored and accessible
```

## 🎯 **Quick Fixes**

### **If Distance Widget Doesn't Appear:**
1. Check both addresses are selected: `viewModel.hasPickupAddress && viewModel.hasDeliveryAddress`
2. Verify coordinates exist: Look for coordinate debug logs
3. Check widget condition in `local_parcel_view.dart`

### **If Distance Shows "Calculating..." Forever:**
1. Check API key configuration
2. Look for error messages in console
3. Verify fallback to straight-line distance works

### **If Coordinates Are Invalid:**
1. Check map picker saves coordinates properly
2. Verify address model stores lat/lng as doubles
3. Test with known good coordinates

## 📞 **Support**

If issues persist after following this guide:
1. Check console logs for specific error messages
2. Verify all dependencies are installed: `flutter pub get`
3. Test with mock coordinates to isolate the issue
4. Ensure proper API key setup if using Google Maps APIs

The distance calculation system is designed with fallbacks, so it should work even without Google Maps API by using straight-line distance calculations.
