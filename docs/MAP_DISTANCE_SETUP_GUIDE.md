# 🗺️ Map & Distance Calculation Setup Guide

This guide will help you implement map functionality and distance calculation between pickup and drop locations in your Flutter app.

## 📋 Prerequisites

1. **Google Cloud Console Account**
2. **Google Maps API Key**
3. **Flutter Development Environment**

## 🔧 Step 1: Google Maps API Setup

### 1.1 Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable billing for the project

### 1.2 Enable Required APIs
Enable these APIs in Google Cloud Console:
- **Maps SDK for Android**
- **Maps SDK for iOS**
- **Geocoding API**
- **Distance Matrix API**
- **Directions API**
- **Places API** (optional, for place search)

### 1.3 Create API Key
1. Go to **APIs & Services > Credentials**
2. Click **Create Credentials > API Key**
3. Copy the API key
4. **Restrict the API key** for security:
   - Application restrictions: Set to your app's package name
   - API restrictions: Limit to the APIs you enabled

### 1.4 Configure API Key in App

**Android** (`android/app/src/main/AndroidManifest.xml`):
```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_ACTUAL_API_KEY_HERE" />
```

**iOS** (`ios/Runner/Info.plist`):
```xml
<key>GMSApiKey</key>
<string>YOUR_ACTUAL_API_KEY_HERE</string>
```

**Services** (`lib/services/distance_service.dart` & `lib/services/location_service.dart`):
```dart
static const String _googleMapsApiKey = 'YOUR_ACTUAL_API_KEY_HERE';
```

## 🚀 Step 2: Install Dependencies

Run this command to install required packages:
```bash
flutter pub get
```

Dependencies added:
- `google_maps_flutter: ^2.5.0` - Google Maps widget
- `location: ^5.0.3` - Location services
- `geocoding: ^3.0.0` - Address geocoding
- `geolocator: ^10.1.0` - GPS location
- `latlong2: ^0.8.1` - Distance calculations

## 📱 Step 3: Platform Configuration

### Android Permissions
Already configured in `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
```

### iOS Permissions
Already configured in `ios/Runner/Info.plist`:
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs location access to calculate distance between pickup and delivery locations.</string>
```

## 🎯 Step 4: Features Implemented

### 4.1 Distance Calculation Service
- **Straight-line distance** using Haversine formula
- **Road distance** using Google Distance Matrix API (driving mode)
- **Fallback mechanism** when API fails

### 4.2 Location Service
- **Current location** detection with permissions
- **Reverse geocoding** (coordinates → address)
- **Forward geocoding** (address → coordinates)
- **Place search** using Google Places API

### 4.3 Distance Calculator Widget
- **Real-time distance calculation**
- **Visual distance display**
- **Address information** display
- **Loading and error states**

### 4.4 Map Integration
- **Interactive map** for location selection
- **Current location** button
- **Address form** with validation
- **Real-time reverse geocoding**

## 🔄 Step 5: Usage Flow

### 5.1 User Journey
1. User selects pickup location → Address saved with coordinates
2. User selects drop location → Address saved with coordinates
3. **Distance calculator automatically appears**
4. Shows distance between locations
5. Distance info used for pricing calculation

### 5.2 Code Integration
The distance calculator is automatically shown when both addresses are selected:

```dart
// In local_parcel_view.dart
if (viewModel.hasPickupAddress && viewModel.hasDeliveryAddress) ...[
  DistanceCalculatorWidget(
    pickupLat: viewModel.pickupDetails?['latitude']?.toDouble(),
    pickupLng: viewModel.pickupDetails?['longitude']?.toDouble(),
    dropLat: viewModel.deliveryDetails?['latitude']?.toDouble(),
    dropLng: viewModel.deliveryDetails?['longitude']?.toDouble(),
    onDistanceCalculated: (result) {
      viewModel.setDistanceResult(result);
    },
  ),
],
```

## 💰 Step 6: API Pricing Considerations

### Google Maps API Costs
- **Distance Matrix API**: $5 per 1,000 requests
- **Geocoding API**: $5 per 1,000 requests
- **Maps SDK**: $7 per 1,000 map loads
- **Directions API**: $5 per 1,000 requests

### Cost Optimization
1. **Cache results** for same route combinations
2. **Use straight-line distance** as fallback
3. **Implement request throttling**
4. **Set API quotas** in Google Cloud Console
5. **Monitor usage** regularly

## 🧪 Step 7: Testing

### 7.1 Test Distance Calculation
```dart
// Test straight-line distance
final distance = DistanceService.calculateStraightLineDistance(
  lat1: 28.6139, lon1: 77.2090, // Delhi
  lat2: 28.4595, lon2: 77.0266, // Gurgaon
);
print('Distance: ${distance.toStringAsFixed(2)} km');
```

### 7.2 Test Location Services
```dart
// Test current location
final locationService = LocationService();
final location = await locationService.getCurrentLocation();
print('Current location: ${location.latitude}, ${location.longitude}');
```

## 🔒 Step 8: Security Best Practices

1. **Restrict API keys** to specific apps/domains
2. **Use environment variables** for API keys in production
3. **Implement rate limiting** on your backend
4. **Monitor API usage** for unusual activity
5. **Rotate API keys** periodically

## 🐛 Step 9: Troubleshooting

### Common Issues

**"API key not found"**
- Check API key is correctly set in platform files
- Ensure API key has proper restrictions
- Verify APIs are enabled in Google Cloud Console

**"Location permission denied"**
- Check permission strings in Info.plist (iOS)
- Verify permissions in AndroidManifest.xml (Android)
- Handle permission requests in code

**"Distance calculation fails"**
- Check internet connectivity
- Verify API quotas not exceeded
- Ensure coordinates are valid
- Check API key restrictions

**"Map not loading"**
- Verify Google Maps SDK is enabled
- Check API key configuration
- Ensure proper platform setup

## 📈 Step 10: Production Deployment

### Before Going Live
1. **Replace API key** with production key
2. **Set up monitoring** for API usage
3. **Implement error tracking**
4. **Test on real devices**
5. **Configure proper API restrictions**
6. **Set up billing alerts**

### Performance Optimization
1. **Cache distance calculations**
2. **Implement request batching**
3. **Use appropriate map zoom levels**
4. **Optimize image assets**
5. **Implement lazy loading**

## 🎉 Conclusion

You now have a complete map and distance calculation system that:
- ✅ Calculates accurate distances between locations
- ✅ Uses driving mode for road distance calculations
- ✅ Handles location permissions properly
- ✅ Provides fallback mechanisms
- ✅ Integrates seamlessly with your parcel booking flow

The system is production-ready and follows best practices for performance, security, and user experience.
